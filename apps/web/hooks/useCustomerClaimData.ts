import { MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';

export const useCustomerClaimData = (customerClaim: ApiBody<'getCustomerClaim'>) => {
	const { data: productDefectsData } = apiHooks.useGetAllProductDefects({
		queries: {
			sort: [['createdAt', 'asc']],
			page: 1,
			limit: MAX_POSITIVE_INTEGER,
			filter: {
				customerClaimId: {
					eq: customerClaim.id,
				},
			},
		},
	});
	const productDefects = productDefectsData?._data ?? [];
	const referenceDefect = productDefects[0];

	const product = customerClaim.ecommerceOrderItem?.product;

	return {
		product,
		referenceDefect,
		productDefects,
	};
};
