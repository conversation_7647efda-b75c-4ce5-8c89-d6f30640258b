import { type CustomerClaimFileType } from '@pocitarna-nx-2023/config';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { useMemo } from 'react';

type CustomerClaimFileGroup = { type: CustomerClaimFileType; files: ApiBody<'getCustomerClaimFiles'>[number]['file'][] };

export const useGroupCustomerClaimFiles = ({
	customerClaimFiles,
	isNewClaim,
	isWaitingForFinalization,
}: {
	customerClaimFiles: ApiBody<'getCustomerClaimFiles'>;
	isNewClaim?: boolean;
	isWaitingForFinalization?: boolean;
}) => {
	return useMemo(
		() =>
			customerClaimFiles
				.filter(({ type }) => {
					if (isNewClaim) return type === 'receipt';
					if (isWaitingForFinalization) return type === 'final';
					return type !== 'creation';
				})
				.reduce((acc: CustomerClaimFileGroup[], { file, type }: ApiBody<'getCustomerClaimFiles'>[number]) => {
					const group = acc.find((group) => group.type === type);
					if (group) {
						group.files.push(file);
					} else {
						acc.push({ type, files: [file] });
					}
					return acc;
				}, []),
		[customerClaimFiles, isNewClaim, isWaitingForFinalization],
	);
};
