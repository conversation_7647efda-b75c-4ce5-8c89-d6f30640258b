import { CustomerClaimStatusMessage } from '@pocitarna-nx-2023/config';
import { Badge } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
};

export const CustomerClaimBadge: FC<Props> = ({ customerClaim }) => {
	const { status } = customerClaim;
	return <Badge variant="info">{CustomerClaimStatusMessage[status]}</Badge>;
};
