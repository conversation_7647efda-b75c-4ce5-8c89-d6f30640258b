import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { Button, MutedText, Stack, TableCell, TableRow } from '@pocitarna-nx-2023/ui';
import {
	formatDateTime,
	formatEnvelopeCode,
	formatProductCode,
	formatServiceCaseCode,
	formatWarrantyClaimCode,
} from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import Link from 'next/link';
import { type FC, Fragment } from 'react';
import { useCustomerClaimData } from '../../hooks/useCustomerClaimData';
import { ProductDefectTypeDisplay } from '../ProductDefectTypeDisplay';
import { CustomerClaimBadge } from './CustomerClaimBadge';
import { CustomerClaimCountdown } from './CustomerClaimCountdown';

type Props = {
	customerClaim: ApiBody<'getCustomerClaims'>[number];
};

export const CustomerClaimRow: FC<Props> = ({ customerClaim }) => {
	const { product, productDefects } = useCustomerClaimData(customerClaim);
	const relatedWarrantyClaim = customerClaim.warrantyClaims[0];
	const relatedServiceCase = customerClaim.serviceCases[0];

	if (!product) return null;

	return (
		<TableRow>
			<TableCell>
				<>
					<Link className="text-link" href={`/product/${product.id}`}>
						{formatProductCode(product.code)}
					</Link>
					<br />
					{product.sn.toUpperCase()}
				</>
			</TableCell>

			<TableCell>
				{formatDateTime(customerClaim.createdAt)} <br /> {customerClaim.createdBy?.name ?? NOT_AVAILABLE}
			</TableCell>
			<TableCell>
				{product.productEnvelope ? (
					<Stack gap={1}>
						{product.productCategory && (
							<Link className="text-link" href={`/product/envelope/${product.productEnvelope.id}`}>
								{formatEnvelopeCode(product.productCategory.codePrefix)(product.productEnvelope.code)}
							</Link>
						)}
						<Link className="text-link" href={`/product/envelope/${product.productEnvelope.id}`}>
							{product.productEnvelope.name}
						</Link>
					</Stack>
				) : (
					NOT_AVAILABLE
				)}
			</TableCell>
			<TableCell>
				{product.productCategory ? (
					<Link className="text-link" href={`/product/envelope/category/${product.productCategory.id}`}>
						{product.productCategory.name}
					</Link>
				) : (
					NOT_AVAILABLE
				)}
			</TableCell>
			<TableCell>
				{productDefects.length > 0
					? productDefects.map((productDefect, index, array) => (
							<Fragment key={productDefect.id}>
								<ProductDefectTypeDisplay productDefect={productDefect} />
								{index < array.length - 1 && (
									<>
										, <br />
									</>
								)}
							</Fragment>
						))
					: NOT_AVAILABLE}
			</TableCell>
			<TableCell>
				<CustomerClaimBadge customerClaim={customerClaim} />

				{relatedServiceCase && (
					<div className="mt-2">
						<MutedText>
							Servis:{' '}
							<Link className="text-link" href={`/service/${relatedServiceCase.id}`}>
								{formatServiceCaseCode(relatedServiceCase.code)}
							</Link>
						</MutedText>
					</div>
				)}

				{relatedWarrantyClaim && (
					<div className="mt-2">
						<MutedText>
							Dod. reklamace:{' '}
							<Link className="text-link" href={`/warranty-claim/${relatedWarrantyClaim.id}`}>
								{formatWarrantyClaimCode(relatedWarrantyClaim.code)}
							</Link>
						</MutedText>
					</div>
				)}
			</TableCell>
			<TableCell>
				<CustomerClaimCountdown customerClaim={customerClaim} />
			</TableCell>

			<TableCell sticky="right">
				<Stack direction="row" gap={2} className="justify-end">
					<Button size="sm" asChild>
						<Link href={`/customer-claim/${customerClaim.id}`}>Detail</Link>
					</Button>
				</Stack>
			</TableCell>
		</TableRow>
	);
};
