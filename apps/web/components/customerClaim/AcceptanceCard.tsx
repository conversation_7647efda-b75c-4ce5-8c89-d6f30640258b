import { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader, ComboboxControl, FormContext, Stack } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';

const OPTIONS = ['NULL', 'ACCEPT', 'REJECT'] as const;
type Option = (typeof OPTIONS)[number];

const schema = z.object({
	accepted: z.enum(OPTIONS),
});

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
	onClaimUpdate: () => void;
};

export const AcceptanceCard: FC<Props> = ({ customerClaim, onClaimUpdate }) => {
	const { mutate, isLoading } = apiHooks.useUpdateCustomerClaim({ params: { customerClaimId: customerClaim.id } });

	const setAcceptanceTimestamp = (option: Option) => {
		if (option === 'NULL') {
			return mutate(
				{
					acceptedAt: null,
					rejectedAt: null,
				},
				{
					onSuccess: () => {
						onClaimUpdate();
					},
				},
			);
		}

		const shouldAccept = option === 'ACCEPT';
		mutate(
			{
				acceptedAt: shouldAccept ? new Date() : null,
				rejectedAt: shouldAccept ? null : new Date(),
			},
			{
				onSuccess: () => {
					onClaimUpdate();
				},
			},
		);
	};

	const getCurrentValue = () => {
		if (customerClaim.acceptedAt) return 'ACCEPT';
		if (customerClaim.rejectedAt) return 'REJECT';
		return 'NULL';
	};

	return (
		<Card>
			<CardHeader>
				<Stack gap={2} direction="row">
					<h3 className="font-semibold leading-none tracking-tight">Rozhodnutí</h3>{' '}
					<AcceptanceBadge customerClaim={customerClaim} />
				</Stack>
			</CardHeader>
			<CardContent>
				<FormContext
					schema={schema}
					defaultValues={{
						accepted: getCurrentValue(),
					}}
				>
					{(control) => (
						<Stack gap={4}>
							<ComboboxControl
								control={control}
								name="accepted"
								items={[
									{ value: 'NULL' as const, label: '-' },
									{ value: 'ACCEPT' as const, label: 'Přijmout reklamaci' },
									{ value: 'REJECT' as const, label: 'Zamítnout reklamaci' },
								]}
								onSelect={(value) => setAcceptanceTimestamp(value as Option)}
								hideSearch={true}
								disabled={isLoading}
							/>
						</Stack>
					)}
				</FormContext>
			</CardContent>
		</Card>
	);
};

const AcceptanceBadge: FC<Pick<Props, 'customerClaim'>> = ({ customerClaim }) => {
	const { acceptedAt, rejectedAt } = customerClaim;

	if (!acceptedAt && !rejectedAt) return null;

	const variant = acceptedAt ? 'success' : 'destructive';
	const label = acceptedAt ? 'Uznaná' : 'Neuznaná';

	return <Badge variant={variant}>{label}</Badge>;
};
