import { CUSTOMER_CLAIM_STATUS_NAMES } from '@pocitarna-nx-2023/config';
import { Button, FormContext, Stack, toast } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';
import { CustomerClaimCosmeticDefectsHandling } from './CustomerClaimCosmeticDefectsHandling';

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
	product: ApiBody<'getProduct'>;
};

export const CustomerClaimResolveForm: FC<Props> = ({ customerClaim, product }) => {
	const { mutate, isLoading } = apiHooks.useUpdateCustomerClaim({ params: { customerClaimId: customerClaim.id } });
	const { invalidate: invalidateCustomerClaim } = apiHooks.useGetCustomerClaim(
		{ params: { customerClaimId: customerClaim.id } },
		{ enabled: false },
	);
	const { invalidate: invalidateFiles } = apiHooks.useGetPublicCustomerClaimFiles(
		{
			params: { customerClaimId: customerClaim.id },
		},
		{ enabled: false },
	);

	return (
		<Stack gap={4}>
			<FormContext
				schema={z.object({
					status: z.enum(CUSTOMER_CLAIM_STATUS_NAMES),
				})}
				defaultValues={{ status: 'RESOLVED' as const }}
				onSubmit={(data) => {
					mutate(
						{
							...data,
							receivedAt: new Date(),
						},
						{
							onSuccess: () => {
								toast.success('Zaktualizováno');
								invalidateCustomerClaim();
								invalidateFiles();
							},
						},
					);
				}}
			>
				{() => {
					return (
						<Stack gap={4}>
							<CustomerClaimCosmeticDefectsHandling customerClaim={customerClaim} product={product} />
							<Button className="w-full" variant="default" type="submit" isLoading={isLoading}>
								Vyřešit reklamaci
							</Button>
						</Stack>
					);
				}}
			</FormContext>
		</Stack>
	);
};
