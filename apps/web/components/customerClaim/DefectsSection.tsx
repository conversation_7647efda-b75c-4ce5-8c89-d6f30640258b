import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { CustomerClaimCosmeticDefectsHandling } from './CustomerClaimCosmeticDefectsHandling';
import { CustomerClaimReceiptForm } from './CustomerClaimReceiptForm';
import { CustomerClaimResolveForm } from './CustomerClaimResolveForm';

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
	product: ApiBody<'getProduct'>;
};

export const DefectsSection: FC<Props> = ({ customerClaim, product }) => {
	const { status } = customerClaim;

	if (status === 'NEW') return <CustomerClaimReceiptForm customerClaim={customerClaim} product={product} />;
	if (status === 'WAITING_FOR_RESOLUTION') return <CustomerClaimResolveForm customerClaim={customerClaim} product={product} />;

	return <CustomerClaimCosmeticDefectsHandling customerClaim={customerClaim} product={product} interactive={false} />;
};
