import { type CustomerClaimFileType, CustomerClaimFileTypeMap } from '@pocitarna-nx-2023/config';
import { Media, ParamItem, ParamList, ParamSpacer } from '@pocitarna-nx-2023/ui';
import { formatCustomerClaimCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, Fragment } from 'react';
import { useGroupCustomerClaimFiles } from '../../hooks/useGroupCustomerClaimFiles';
import { FileUploader } from '../FileUploader';

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
	customerClaimFiles: ApiBody<'getCustomerClaimFiles'>;
	disabled?: boolean;
};

type CustomerClaimFileGroup = { type: CustomerClaimFileType; files: ApiBody<'getCustomerClaimFiles'>[number]['file'][] };

export const CustomerClaimFilesCard: FC<Props> = ({ customerClaim, customerClaimFiles, disabled }) => {
	const isNewClaim = customerClaim.status === 'NEW';
	const isWaitingForFinalization = customerClaim.status === 'WAITING_FOR_RESOLUTION';
	const fileTypeToUpload = isNewClaim ? 'receipt' : isWaitingForFinalization ? 'final' : 'regular';

	const { invalidate: invalidateCustomerClaimFiles } = apiHooks.useGetCustomerClaimFiles(
		{
			params: { customerClaimId: customerClaim.id },
		},
		{ enabled: false },
	);
	const { mutate: addFilesToCustomerClaim, isLoading: isAddingFiles } = apiHooks.useAddFilesToCustomerClaim(
		{ params: { customerClaimId: customerClaim.id }, queries: { type: fileTypeToUpload } },
		{ onSuccess: () => invalidateCustomerClaimFiles() },
	);
	const { mutate: deleteFiles, isLoading: isDeletingFiles } = apiHooks.useDeleteFiles(undefined, {
		onSuccess: () => invalidateCustomerClaimFiles(),
	});
	const { mutate: rotateImageFile, isLoading: isRotatingFile } = apiHooks.useRotateImageFile(undefined, {
		onSuccess: () => invalidateCustomerClaimFiles(),
	});
	const { mutate: updateSequence } = apiHooks.useUpdateCustomerClaimFileSequence(
		{ params: { customerClaimId: customerClaim.id } },
		{ onSuccess: () => invalidateCustomerClaimFiles() },
	);

	const customerClaimFileGroups = useGroupCustomerClaimFiles({ customerClaimFiles, isNewClaim, isWaitingForFinalization });

	return (
		<FileUploader
			pageType="detail"
			inputName="files"
			entityCode={formatCustomerClaimCode(customerClaim.code)}
			entityId={customerClaim.id}
			entityType="customerClaim"
			isAddingFiles={isAddingFiles}
			addFilesToEntity={addFilesToCustomerClaim}
			disabled={disabled}
			cardTitle={customerClaim.status === 'NEW' ? 'Přijetí reklamovaného produktu' : undefined}
		>
			<ParamList lineBreak={true} className="empty:hidden">
				{customerClaimFileGroups.map(({ type, files }: CustomerClaimFileGroup, index) => (
					<Fragment key={type}>
						{index > 0 && <ParamSpacer lineBreak={true} />}
						<ParamItem label={CustomerClaimFileTypeMap[type]}>
							<Media
								files={files}
								moveTo={disabled ? undefined : (file, sequence) => updateSequence({ fileId: file.id, sequence })}
								onDelete={disabled ? undefined : (file) => deleteFiles({ ids: [file.id] })}
								onRotation={disabled ? undefined : (file, rotation) => rotateImageFile({ fileId: file.id, rotation })}
								isLoading={isAddingFiles || isDeletingFiles || isRotatingFile}
							/>
						</ParamItem>
					</Fragment>
				))}
			</ParamList>
		</FileUploader>
	);
};
