import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON>eader,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>alogTrigger,
	FormContext,
	Icon,
	Stack,
	TextControl,
	toast,
	useDialog,
} from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { z } from 'zod';

type Props = {
	customerClaimId: string;
};

export const MessageSender: FC<Props> = ({ customerClaimId }) => {
	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button className="w-full" variant="secondary">
					<Icon name="envelope" />
					Odeslat zprávu
				</Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Odeslat zprávu</DialogTitle>
				</DialogHeader>
				<MessageCreation customerClaimId={customerClaimId} />
			</DialogContent>
		</Dialog>
	);
};

const MessageCreation: FC<Props> = ({ customerClaimId }) => {
	const { mutate, isLoading } = apiHooks.useSendCustomerClaimMessage({ params: { customerClaimId } });
	const { invalidate: invalidateCustomerClaimHistory } = apiHooks.useGetCustomerClaimHistory(
		{ params: { customerClaimId } },
		{ enabled: false },
	);
	const { invalidate: invalidatePublicCustomerClaimHistory } = apiHooks.useGetPublicCustomerClaimHistory(
		{ params: { customerClaimId } },
		{ enabled: false },
	);
	const { closeDialog } = useDialog();

	return (
		<FormContext
			schema={z.object({ message: z.string().min(1) })}
			defaultValues={{
				message: '',
			}}
			onSubmit={(data) => {
				mutate(data, {
					onSuccess: () => {
						invalidateCustomerClaimHistory();
						invalidatePublicCustomerClaimHistory();
						closeDialog();
						toast.success('Zpráva odeslána');
					},
				});
			}}
		>
			{(control) => (
				<Stack gap={4}>
					<TextControl control={control} name="message" />

					<DialogFooter>
						<Button className="w-full" variant="default" type="submit" isLoading={isLoading}>
							Vytvořit
						</Button>
					</DialogFooter>
				</Stack>
			)}
		</FormContext>
	);
};
