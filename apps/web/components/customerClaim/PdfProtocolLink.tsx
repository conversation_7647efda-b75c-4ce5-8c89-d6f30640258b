import { API_ORIGIN } from '@pocitarna-nx-2023/config';
import { Button } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
};

export const PdfProtocolLink: FC<Props> = ({ customerClaim }) => {
	if (customerClaim.status === 'NEW') return null;

	return (
		<Button variant="secondary" asChild>
			<a href={`${API_ORIGIN}/customer-claim/${customerClaim.id}/protocol`} target="_blank" download>
				Vytisknout reklamační protokol (PDF)
			</a>
		</Button>
	);
};
