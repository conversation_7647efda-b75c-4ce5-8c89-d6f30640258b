import { Button } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { type CustomerClaimFlowHandlerProps } from './CustomerClaimFlowHandler';

export const FromResolvedToSentBack: FC<CustomerClaimFlowHandlerProps> = ({ customerClaim, onClaimUpdate }) => {
	const { mutate, isLoading } = apiHooks.useUpdateCustomerClaim({ params: { customerClaimId: customerClaim.id } });

	return (
		<Button
			type="button"
			onClick={() => {
				mutate(
					{ status: 'SENT_BACK' as const },
					{
						onSuccess: () => {
							onClaimUpdate();
						},
					},
				);
			}}
			isLoading={isLoading}
		>
			Odeslat produkt zpět
		</Button>
	);
};
