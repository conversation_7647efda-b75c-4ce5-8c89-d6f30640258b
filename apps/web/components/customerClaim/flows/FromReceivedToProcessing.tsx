import { Button, Stack } from '@pocitarna-nx-2023/ui';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useMemo } from 'react';
import { type CustomerClaimFlowHandlerProps } from './CustomerClaimFlowHandler';

export const FromReceivedToProcessing: FC<CustomerClaimFlowHandlerProps> = ({ productId, customerClaim, onClaimUpdate }) => {
	const { mutate, isLoading } = apiHooks.useUpdateCustomerClaim({ params: { customerClaimId: customerClaim.id } });

	const { data: productWarrantiesData } = apiHooks.useGetProductWarranties({ params: { productId } });

	const buyWarranty = useMemo(() => productWarrantiesData?._data?.buyWarranty, [productWarrantiesData?._data?.buyWarranty]);
	const isInVendorWarranty = useMemo(() => buyWarranty?.expiredAt && buyWarranty.expiredAt > new Date(), [buyWarranty?.expiredAt]);
	const label = isInVendorWarranty ? 'Vyřešit prostřednictvím dodavatelské reklamace' : 'Vyřešit prostřednictvím servisu';

	if (customerClaim.rejectedAt) return null;

	return (
		<Stack gap={2}>
			<Button
				type="button"
				onClick={() => {
					mutate(
						{ status: 'PROCESSING' as const },
						{
							onSuccess: () => {
								onClaimUpdate();
							},
						},
					);
				}}
				isLoading={isLoading}
			>
				{label}
			</Button>

			<Button
				variant="destructive"
				onClick={() => {
					mutate(
						{ acceptedAt: null, rejectedAt: new Date() },
						{
							onSuccess: () => {
								onClaimUpdate();
							},
						},
					);
				}}
				isLoading={isLoading}
			>
				Zamítnout reklamaci
			</Button>
		</Stack>
	);
};
