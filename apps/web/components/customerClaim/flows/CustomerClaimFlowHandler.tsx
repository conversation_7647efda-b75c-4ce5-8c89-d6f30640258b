import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { FromReceivedToProcessing } from './FromReceivedToProcessing';
import { FromResolvedToSentBack } from './FromResolvedToSentBack';

type Props = {
	product: ApiBody<'getProduct'>;
	customerClaim: ApiBody<'getCustomerClaim'>;
	disabled?: boolean;
	onClaimUpdate: () => void;
};

export type CustomerClaimFlowHandlerProps = {
	customerClaim: ApiBody<'getCustomerClaim'>;
	productId: string;
	onClaimUpdate: () => void;
};

export const CustomerClaimFlowHandler: FC<Props> = ({ customerClaim, product, onClaimUpdate }) => {
	const { status } = customerClaim;
	const handlerProps: CustomerClaimFlowHandlerProps = {
		customerClaim,
		productId: product.id,
		onClaimUpdate,
	};

	if (status === 'RECEIVED') {
		return <FromReceivedToProcessing {...handlerProps} />;
	}
	if (status === 'RESOLVED') return <FromResolvedToSentBack {...handlerProps} />;

	return null;
};
