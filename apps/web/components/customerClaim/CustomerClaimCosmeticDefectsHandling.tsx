import { Stack, Table, TableBody, TableCell, TableRow } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC, memo, type PropsWithChildren } from 'react';
import { useCustomerClaimCosmeticDefectsFormData } from '../../hooks/useCosmeticDefectsFormData';
import { CosmeticDefectsByArea } from '../cosmeticDefects/CosmeticDefectsByArea';

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
	product: ApiBody<'getProduct'>;
	interactive?: boolean;
};

export const CustomerClaimCosmeticDefectsHandling: FC<PropsWithChildren<Props>> = memo(({ customerClaim, product, interactive = true }) => {
	const { customerClaimCosmeticDefects, cosmeticAreas, categoryCosmeticDefects } = useCustomerClaimCosmeticDefectsFormData(
		customerClaim,
		product,
	);

	if (!interactive && customerClaimCosmeticDefects.length < 1) return null;

	return (
		<Stack gap={4}>
			<h2 className="h3">Kosmetické vady</h2>
			<Table>
				<TableBody>
					{cosmeticAreas.map((area) => {
						return (
							<TableRow key={area.id}>
								<TableCell>{area.name}</TableCell>
								<TableCell>
									<CosmeticDefectsByArea
										cosmeticAreaId={area.id}
										customerClaim={customerClaim}
										areaCosmeticDefects={categoryCosmeticDefects.filter((defect) =>
											defect.cosmeticAreaCosmeticDefects?.some((item) => item.cosmeticAreaId === area.id),
										)}
										entitiesWithCosmeticDefectsByArea={customerClaimCosmeticDefects.filter(
											(defect) => defect.cosmeticAreaId === area.id,
										)}
										interactive={interactive}
									/>
								</TableCell>
							</TableRow>
						);
					})}
				</TableBody>
			</Table>
		</Stack>
	);
});

CustomerClaimCosmeticDefectsHandling.displayName = 'CustomerClaimCosmeticDefectsHandling';
