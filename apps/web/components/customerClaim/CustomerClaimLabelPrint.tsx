import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useState } from 'react';
import { LabelPrint } from '../printer/LabelPrint';

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
};

export const CustomerClaimLabelPrint: FC<Props> = ({ customerClaim }) => {
	const [shouldPrint, setShouldPrint] = useState(false);

	const { data: printLabelsData, isFetching: isLoadingLabels } = apiHooks.useGetPrintLabelsCustomerClaim(
		{ params: { customerClaimId: customerClaim.id } },
		{ enabled: shouldPrint },
	);

	return (
		<LabelPrint
			buttonLabel="Vytisknout štítek reklamace"
			printLabels={printLabelsData?._data}
			printerLocation="TESTING"
			isLoadingLabels={isLoadingLabels}
			handleClick={() => setShouldPrint(true)}
		/>
	);
};
