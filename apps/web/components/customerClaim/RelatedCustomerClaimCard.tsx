import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, ParamItem, ParamList } from '@pocitarna-nx-2023/ui';
import { formatCustomerClaimCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { type FC } from 'react';

type Props = {
	relatedCustomerClaimId: string | null | undefined;
};

export const RelatedCustomerClaimCard: FC<Props> = ({ relatedCustomerClaimId }) => {
	const { data: relatedClaimData } = apiHooks.useGetCustomerClaim(
		{
			params: { customerClaimId: relatedCustomerClaimId ?? '' },
		},
		{ enabled: Boolean(relatedCustomerClaimId) },
	);

	const relatedClaim = relatedClaimData?._data;

	if (!relatedClaim) return null;

	return (
		<Card>
			<CardHeader className="flex flex-col justify-between items-start md:flex-row">
				<CardTitle>Zákaznická reklamace</CardTitle>
			</CardHeader>
			<CardContent>
				<ParamList>
					<ParamItem label="Reklamace" align="center">
						<Link className="text-link" href={`/customer-claim/${relatedClaim.id}`}>
							{formatCustomerClaimCode(relatedClaim.code)}
						</Link>
					</ParamItem>
				</ParamList>
			</CardContent>
		</Card>
	);
};
