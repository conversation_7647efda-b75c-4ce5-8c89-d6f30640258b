import { Media } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';

type Props = {
	files: ApiBody<'getPublicCustomerClaimFiles'>;
};

export const CustomerClaimCreationFiles: FC<Props> = ({ files }) => {
	const creationFiles = files.filter(({ type }) => type === 'creation');
	return <Media files={creationFiles.map(({ file }) => file)} />;
};
