import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { Card, CardContent, CardHeader, CardTitle, cn, TableCell, TableFooter, TableRow } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { useProductCosmeticDefectsFormData } from '../../hooks/useCosmeticDefectsFormData';
import { CosmeticDefectsBreakdown } from './CosmeticDefectsBreakdown';

type Props = {
	product: ApiBody<'getProduct'>;
};

export const CosmeticDefectsCard: FC<Props> = ({ product }) => {
	const { calculatedGradeId } = useProductCosmeticDefectsFormData(product);
	const gradeWasManuallyChanged = product.gradeId != null && product.gradeId !== calculatedGradeId;

	return (
		<Card>
			<CardHeader>
				<CardTitle>Kosmetické vady</CardTitle>
			</CardHeader>
			<CardContent>
				<CosmeticDefectsBreakdown product={product} isFix={false} interactive={false}>
					<TableFooter>
						<TableRow>
							<TableCell>
								<strong>Stav produktu</strong>
							</TableCell>
							<TableCell className={cn(gradeWasManuallyChanged && 'text-warning')}>
								{product.grade?.name ?? NOT_AVAILABLE} {gradeWasManuallyChanged && '(neodpovídá vadám)'}
							</TableCell>
						</TableRow>
					</TableFooter>
				</CosmeticDefectsBreakdown>
			</CardContent>
		</Card>
	);
};
