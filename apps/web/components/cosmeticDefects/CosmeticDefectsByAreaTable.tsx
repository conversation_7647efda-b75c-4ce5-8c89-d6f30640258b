import { TEN_SECONDS } from '@pocitarna-nx-2023/config';
import { DeleteDialog, Popover, PopoverContent, PopoverTrigger, Stack, Table, TableBody, TableCell, TableRow } from '@pocitarna-nx-2023/ui';
import { formatCustomerClaimCode, formatProductCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useCallback } from 'react';
import { DialogQRUploadPhoto } from '../DialogQRUploadPhoto';
import { CosmeticDefectFiles } from './CosmeticDefectFiles';
import { type WithCosmeticDefect } from './CosmeticDefectsByArea';

type Props = {
	product?: ApiBody<'getProduct'>;
	customerClaim?: ApiBody<'getCustomerClaim'>;
	entitiesWithCosmeticDefectsByArea: WithCosmeticDefect[];
	onSuccess?: () => void;
	interactive?: boolean;
};

export const CosmeticDefectByAreaTable: FC<Props> = ({
	product,
	customerClaim,
	entitiesWithCosmeticDefectsByArea,
	onSuccess,
	interactive = true,
}) => {
	const isProduct = 'productId' in entitiesWithCosmeticDefectsByArea[0];

	return (
		<Table>
			<TableBody>
				{entitiesWithCosmeticDefectsByArea.map((item) => (
					<CosmeticDefectByAreaTableRow
						key={item.id}
						entityWithCosmeticDefect={item}
						product={product}
						customerClaim={customerClaim}
						onSuccess={onSuccess}
						interactive={interactive}
						relatedEntityType={isProduct ? 'product' : 'customerClaim'}
					/>
				))}
			</TableBody>
		</Table>
	);
};

const CosmeticDefectByAreaTableRow: FC<
	Omit<Props, 'entitiesWithCosmeticDefectsByArea'> & {
		entityWithCosmeticDefect: WithCosmeticDefect;
		relatedEntityType: 'product' | 'customerClaim';
	}
> = ({ entityWithCosmeticDefect, product, customerClaim, relatedEntityType, onSuccess, interactive = true }) => {
	const { mutate: deleteCosmeticDefect } = apiHooks.useRemoveCosmeticDefectRelation(undefined);

	const { data: defectFilesData } = apiHooks.useGetCosmeticDefectRelationFiles(
		{
			params: { relatedEntityId: entityWithCosmeticDefect.id },
			queries: { relatedEntityType },
		},
		{ refetchInterval: TEN_SECONDS },
	);
	const defectFiles = defectFilesData?._data ?? [];

	const onDelete = useCallback(() => {
		deleteCosmeticDefect(
			{ cosmeticDefectId: entityWithCosmeticDefect.cosmeticDefectId, productId: product?.id, customerClaimId: customerClaim?.id },
			{
				onSuccess,
			},
		);
	}, [deleteCosmeticDefect, onSuccess, entityWithCosmeticDefect.cosmeticDefectId, product?.id, customerClaim?.id]);

	return (
		<TableRow>
			<TableCell>
				<strong>{entityWithCosmeticDefect.cosmeticDefect.name}</strong>
			</TableCell>
			<TableCell className="w-0">
				{defectFiles.length > 0 && (
					<Popover>
						<PopoverTrigger asChild>
							<span className="text-link cursor-pointer">Média</span>
						</PopoverTrigger>
						<PopoverContent>
							<CosmeticDefectFiles
								defectFiles={defectFiles}
								entityWithCosmeticDefect={entityWithCosmeticDefect}
								interactive={interactive}
								relatedEntityType={relatedEntityType}
							/>
						</PopoverContent>
					</Popover>
				)}
			</TableCell>
			{interactive && (
				<TableCell className="w-0">
					<Stack direction="row" className="justify-end" gap={2}>
						{entityWithCosmeticDefect.cosmeticDefect.pictureRequired && (
							<DialogQRUploadPhoto
								usage="detail"
								entityCode={
									product
										? formatProductCode(product.code)
										: customerClaim
											? formatCustomerClaimCode(customerClaim.code)
											: ''
								}
								entityId={entityWithCosmeticDefect.id}
								entityType="productCosmeticDefect"
								withButtonText={false}
							/>
						)}
						<DeleteDialog
							title="Smazat vadu"
							description="Opravdu chcete smazat položku?"
							onDelete={onDelete}
							triggerVariant="icon"
						/>
					</Stack>
				</TableCell>
			)}
		</TableRow>
	);
};
