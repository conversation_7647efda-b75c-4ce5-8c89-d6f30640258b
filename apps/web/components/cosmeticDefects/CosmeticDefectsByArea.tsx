import { CheckboxControl, ComboboxControl, Stack, toast } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useCallback, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { CosmeticDefectByAreaTable } from './CosmeticDefectsByAreaTable';

export type WithCosmeticDefect = {
	id: string;
	cosmeticDefectId: string;
	cosmeticAreaId: string;
	cosmeticDefect: {
		id: string;
		name: string;
		pictureRequired: boolean;
	};
	productId?: string;
	customerClaimId?: string;
};

type Props = {
	cosmeticAreaId: string;
	product?: ApiBody<'getProduct'>;
	customerClaim?: ApiBody<'getCustomerClaim'>;
	areaCosmeticDefects: ApiBody<'getCosmeticDefects'>;
	entitiesWithCosmeticDefectsByArea: WithCosmeticDefect[];
	interactive?: boolean;
	isFix?: boolean;
};

export const CosmeticDefectsByArea: FC<Props> = ({
	cosmeticAreaId,
	product,
	customerClaim,
	areaCosmeticDefects,
	entitiesWithCosmeticDefectsByArea,
	interactive = true,
	isFix = false,
}) => {
	const { invalidate: invalidateProductCosmeticDefects } = apiHooks.useGetProductCosmeticDefects(
		{ queries: { filter: { productId: { eq: product?.id ?? '' } } } },
		{ enabled: false },
	);

	const { invalidate: invalidateCustomerClaimCosmeticDefects } = apiHooks.useGetCustomerClaimCosmeticDefects(
		{ queries: { filter: { customerClaimId: { eq: customerClaim?.id ?? '' } } } },
		{ enabled: false },
	);

	const onSuccess = useCallback(() => {
		invalidateProductCosmeticDefects();
		invalidateCustomerClaimCosmeticDefects();
		toast.success('Zaktualizováno');
	}, [invalidateProductCosmeticDefects, invalidateCustomerClaimCosmeticDefects]);

	return (
		<Stack gap={4}>
			{interactive && (
				<InteractiveElements
					areaCosmeticDefects={areaCosmeticDefects}
					cosmeticAreaId={cosmeticAreaId}
					product={product}
					customerClaim={customerClaim}
					isFix={isFix}
					onSuccess={onSuccess}
					entitiesWithCosmeticDefectsByArea={entitiesWithCosmeticDefectsByArea}
				/>
			)}

			{entitiesWithCosmeticDefectsByArea.length > 0 && (
				<CosmeticDefectByAreaTable
					product={product}
					customerClaim={customerClaim}
					entitiesWithCosmeticDefectsByArea={entitiesWithCosmeticDefectsByArea}
					onSuccess={onSuccess}
					interactive={interactive}
				/>
			)}

			{entitiesWithCosmeticDefectsByArea.length === 0 && !interactive && (
				<p className="text-muted-foreground">Žádné kosmetické vady v této oblasti</p>
			)}
		</Stack>
	);
};

const InteractiveElements: FC<Props & { onSuccess: () => void }> = ({
	areaCosmeticDefects,
	cosmeticAreaId,
	product,
	customerClaim,
	onSuccess,
	isFix,
	entitiesWithCosmeticDefectsByArea,
}) => {
	const { control, watch } = useFormContext<{ clearCategoryCosmeticAreas: Record<string, boolean> }>();
	const areaIsMarkedAsAllClear = watch(`clearCategoryCosmeticAreas.${cosmeticAreaId}`);

	return (
		<>
			<DefectPicker
				areaCosmeticDefects={areaCosmeticDefects}
				cosmeticAreaId={cosmeticAreaId}
				product={product}
				customerClaim={customerClaim}
				isFix={isFix}
				onSuccess={onSuccess}
				disabled={areaIsMarkedAsAllClear}
			/>

			<CheckboxControl
				control={control}
				name={`clearCategoryCosmeticAreas.${cosmeticAreaId}`}
				label="Žádné kosmetické vady v této oblasti"
				disabled={entitiesWithCosmeticDefectsByArea.length > 0}
			/>
		</>
	);
};

const DefectPicker: FC<
	Omit<Props, 'interactive' | 'entitiesWithCosmeticDefectsByArea'> & { onSuccess: () => void; disabled?: boolean }
> = ({ areaCosmeticDefects, cosmeticAreaId, product, customerClaim, onSuccess, isFix, disabled = false }) => {
	const { control } = useFormContext<{ cosmeticDefects: Record<string, string[]> }>();

	const { mutate: addCosmeticDefect } = apiHooks.useEstablishCosmeticDefectRelation(undefined);

	const valueName = useMemo(() => `cosmeticDefects.${cosmeticAreaId}` as const, [cosmeticAreaId]);

	const onSelect = useCallback(
		(cosmeticDefectId: string) => {
			addCosmeticDefect(
				{ productId: product?.id, customerClaimId: customerClaim?.id, cosmeticAreaId, cosmeticDefectId, isFix },
				{
					onSuccess,
				},
			);
		},
		[addCosmeticDefect, cosmeticAreaId, isFix, onSuccess, product?.id, customerClaim?.id],
	);

	return (
		<ComboboxControl
			control={control}
			name={valueName}
			onSelect={onSelect}
			items={areaCosmeticDefects.map((item) => ({ value: item.id, label: item.name }))}
			placeholder="Vyberte vady"
			searchPlaceholder="Vyhledat vady"
			enableFilter
			disabled={disabled}
		/>
	);
};
