import { Button, FormContext, type FormContextRef, toast, useDialog } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, useEffect, useRef } from 'react';
import { useProductCosmeticDefectsFormData } from '../../hooks/useCosmeticDefectsFormData';
import { CosmeticDefectsHandling } from './CosmeticDefectsHandling';

type Props = {
	product: ApiBody<'getProduct'>;
	isFix?: boolean;
};

export const TestedProductCosmeticDefectsHandling: FC<Props> = ({ product, isFix }) => {
	const { defaultValues, schema, allRelevantDefectsHaveImage } = useProductCosmeticDefectsFormData(product);
	const formRef = useRef<FormContextRef<typeof schema>>(null);

	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId: product.id } }, { enabled: false });
	const { mutate: saveProductTest, isLoading } = apiHooks.useSaveProductTest({ params: { productId: product.id } });
	const { closeDialog } = useDialog();

	useEffect(() => {
		formRef?.current?.reset(defaultValues);
	}, [defaultValues]);

	return (
		<FormContext
			schema={schema}
			defaultValues={defaultValues}
			ref={formRef}
			onSubmit={(data) => {
				if (!allRelevantDefectsHaveImage) {
					toast.error('Nahrajte prosím fotografii pro všechny relevantní kosmetické vady.');
					return;
				}

				saveProductTest(
					{
						gradeId: data.gradeId,
						productType: data.type,
					},
					{
						onSuccess: () => {
							invalidateProduct();
							toast.success('Zaktualizováno');
							closeDialog();
						},
					},
				);
			}}
		>
			{() => (
				<CosmeticDefectsHandling product={product} isFix={isFix}>
					<Button className="w-full" variant="default" type="submit" isLoading={isLoading}>
						Uložit
					</Button>
				</CosmeticDefectsHandling>
			)}
		</FormContext>
	);
};
