import { Media } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { type WithCosmeticDefect } from './CosmeticDefectsByArea';

type Props = {
	entityWithCosmeticDefect: WithCosmeticDefect;
	relatedEntityType: 'product' | 'customerClaim';
	defectFiles: ApiBody<'getCosmeticDefectRelationFiles'>;
	interactive: boolean;
	disabled?: boolean;
};

export const CosmeticDefectFiles: FC<Props> = ({
	entityWithCosmeticDefect,
	relatedEntityType,
	disabled = false,
	defectFiles,
	interactive,
}) => {
	const isProduct = relatedEntityType === 'product';

	const { invalidate: invalidateProductCosmeticDefects } = apiHooks.useGetProductCosmeticDefects(
		{
			queries: {
				filter: {
					productId: {
						eq: entityWithCosmeticDefect?.productId ?? '',
					},
				},
			},
		},
		{ enabled: false },
	);

	const { invalidate: invalidateCustomerClaimCosmeticDefects } = apiHooks.useGetCustomerClaimCosmeticDefects(
		{
			queries: {
				filter: {
					customerClaimId: {
						eq: entityWithCosmeticDefect?.customerClaimId ?? '',
					},
				},
			},
		},
		{ enabled: false },
	);

	const { invalidate: invalidateDefectFiles } = apiHooks.useGetCosmeticDefectRelationFiles(
		{
			params: { relatedEntityId: entityWithCosmeticDefect.id },
			queries: { relatedEntityType: isProduct ? 'product' : 'customerClaim' },
		},
		{ enabled: false },
	);

	const onSuccess = () => {
		invalidateProductCosmeticDefects();
		invalidateCustomerClaimCosmeticDefects();
		invalidateDefectFiles();
	};

	const { mutate: deleteFiles, isLoading: isDeletingFiles } = apiHooks.useDeleteFiles(undefined, {
		onSuccess,
	});
	const { mutate: rotateImageFile, isLoading: isRotatingFile } = apiHooks.useRotateImageFile(undefined, {
		onSuccess,
	});

	const { mutate: updateSequence } = apiHooks.useUpdateCosmeticDefectRelationFileSequence(
		{
			params: { relatedEntityId: entityWithCosmeticDefect.id },
			queries: { relatedEntityType: isProduct ? 'product' : 'customerClaim' },
		},
		{ onSuccess },
	);

	return (
		<Media
			files={defectFiles.map(({ file }) => file)}
			moveTo={!disabled && interactive ? (file, sequence) => updateSequence({ fileId: file.id, sequence }) : undefined}
			onDelete={disabled || !interactive ? undefined : (file) => deleteFiles({ ids: [file.id] })}
			onRotation={disabled || !interactive ? undefined : (file, rotation) => rotateImageFile({ fileId: file.id, rotation })}
			isLoading={isDeletingFiles || isRotatingFile}
		/>
	);
};
