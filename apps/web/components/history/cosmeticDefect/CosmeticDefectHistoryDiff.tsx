import { cn } from '@pocitarna-nx-2023/ui';
import { parsePeriod } from '@pocitarna-nx-2023/utils';
import { type relatedCosmeticDefect } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { type z } from 'zod';

type Props = {
	current?: z.infer<typeof relatedCosmeticDefect> & { period: string };
	previous?: z.infer<typeof relatedCosmeticDefect> & { period: string };
};

export const CosmeticDefectHistoryDiff: FC<Props> = ({ current, previous }) => {
	if (!current) return null;
	const isDelete = previous && !!parsePeriod(current.period)[1];
	const isChange = previous && !parsePeriod(current.period)[1];
	const isNew = !previous;

	return (
		<div>
			{isDelete && 'Smazaná'}
			{isChange && 'Změněná'}
			{isNew && 'Nová'} kosmetická závada:{' '}
			<span className={cn(isDelete && 'text-destructive line-through')}>
				{current.cosmeticArea.name} - {current.cosmeticDefect.name}
			</span>
		</div>
	);
};
