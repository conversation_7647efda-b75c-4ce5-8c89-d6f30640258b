import type { serviceCenter } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import type { z } from 'zod';
import { type KeyDiffMap, KeyDiffViewer } from '../KeyDiffViewer';
import { MarkdownRenderer } from '../MarkdownRenderer';

type Props = {
	current?: z.infer<typeof serviceCenter>;
	previous?: z.infer<typeof serviceCenter>;
};

const keyDiffMap: KeyDiffMap<z.infer<typeof serviceCenter>> = {
	createdAt: {
		label: 'Vytvořeno',
	},
	name: {
		label: '<PERSON><PERSON>zev',
	},
	note: {
		label: 'Poz<PERSON>m<PERSON>',
		renderer: Markdown<PERSON>enderer,
	},
	addressId: {
		label: '<PERSON>res<PERSON>',
	},
	contactId: {
		label: 'Kontakt',
	},
};

export const ServiceCenterHistoryDiff: FC<Props> = (props) => {
	return <KeyDiffViewer keyDiffMap={keyDiffMap} {...props} />;
};
