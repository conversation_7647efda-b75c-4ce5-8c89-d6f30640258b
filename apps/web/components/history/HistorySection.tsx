import { <PERSON><PERSON><PERSON>, Spinner, Stack } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC, type ReactNode } from 'react';
import { getHistoryEndpointBadge, getHistoryEndpointTitle } from '../../utils/historyEndpointMap';
import { BatchHistoryDiff } from './batch/BatchHistoryDiff';
import { CosmeticDefectHistoryDiff } from './cosmeticDefect/CosmeticDefectHistoryDiff';
import { CustomerClaimHistoryDiff, PublicCustomerClaimHistoryDiff } from './customerClaim/CustomerClaimHistoryDiff';
import { CustomerClaimMessageHistoryDiff } from './customerClaim/CustomerClaimMessageHistoryDiff';
import { FileHistoryDiff } from './file/FileHistoryDiff';
import { MarkdownRenderer } from './MarkdownRenderer';
import { ProductAttributeValueHistoryDiff } from './product/ProductAttributeValueHistoryDiff';
import { ProductHistoryDiff } from './product/ProductHistoryDiff';
import { ProductCosmeticDefectHistoryDiff } from './productCosmeticDefect/ProductCosmeticDefectHistoryDiff';
import { ProductDefectHistoryDiff } from './productDefect/ProductDefectHistoryDiff';
import { ProductEnvelopeHistoryDiff } from './productEnvelope/ProductEnvelopeHistoryDiff';
import { ServiceCaseHistoryDiff } from './serviceCase/ServiceCaseHistoryDiff';
import { ServiceCenterHistoryDiff } from './serviceCenter/ServiceCenterHistoryDiff';
import { ShipmentHistoryDiff } from './shipment/ShipmentHistoryDiff';
import { VendorHistoryDiff } from './vendor/VendorHistoryDiff';
import { WarehousePositionHistoryDiff } from './warehousePosition/WarehousePositionHistoryDiff';
import { WarehousePositionProductHistoryDiff } from './warehousePosition/WarehousePositionProductHistoryDiff';
import { WarrantyClaimHistoryDiff } from './warrantyClaim/WarrantyClaimHistoryDiff';

type Props = {
	history:
		| ApiBody<'getBatchHistory'>
		| ApiBody<'getProductHistory'>
		| ApiBody<'getProductEnvelopeHistory'>
		| ApiBody<'getServiceCaseHistory'>
		| ApiBody<'getServiceCenterHistory'>
		| ApiBody<'getShipmentHistory'>
		| ApiBody<'getVendorHistory'>
		| ApiBody<'getWarehousePositionHistory'>
		| ApiBody<'getWarrantyClaimHistory'>;
	children?: ReactNode;
	isLoading: boolean;
};

const entityMap: Record<string, FC<{ current?: any; previous?: any }>> = {
	batch: BatchHistoryDiff,
	cosmeticDefect: CosmeticDefectHistoryDiff,
	customerClaim: CustomerClaimHistoryDiff,
	customerClaimMessage: CustomerClaimMessageHistoryDiff,
	fileBatch: FileHistoryDiff,
	fileBatchDefect: FileHistoryDiff,
	fileCustomerClaim: FileHistoryDiff,
	fileProduct: FileHistoryDiff,
	fileProductDefect: FileHistoryDiff,
	fileServiceCase: FileHistoryDiff,
	fileVendor: FileHistoryDiff,
	fileWarrantyClaim: FileHistoryDiff,
	product: ProductHistoryDiff,
	productAttributeValue: ProductAttributeValueHistoryDiff,
	productEnvelope: ProductEnvelopeHistoryDiff,
	serviceCase: ServiceCaseHistoryDiff,
	serviceCenter: ServiceCenterHistoryDiff,
	shipment: ShipmentHistoryDiff,
	vendor: VendorHistoryDiff,
	warehousePosition: WarehousePositionHistoryDiff,
	warehousePositionProduct: WarehousePositionProductHistoryDiff,
	warrantyClaim: WarrantyClaimHistoryDiff,
	productDefect: ProductDefectHistoryDiff,
	productCosmeticDefect: ProductCosmeticDefectHistoryDiff,
	publicCustomerClaim: PublicCustomerClaimHistoryDiff,
};

export const HistorySection = ({ history, children, isLoading }: Props) => {
	if (isLoading) return <Spinner />;
	if (!history.length) return children;

	return history.map((record, actionIndex) => {
		return (
			<HistoryItem
				key={record.action.id}
				user={record.action.authentication.user}
				timestamp={record.action.createdAt}
				verb={getHistoryEndpointTitle(record.action.endpoint, record.action.method)}
				actions={actionIndex === 0 && children}
				badge={getHistoryEndpointBadge(record.action.endpoint)}
			>
				{Object.keys(record.entities).map((key) => {
					const Component = entityMap[key];
					if (!Component) return;
					const entities = record.entities[key] as { id: string }[];

					return (
						<Stack key={key} gap={1}>
							{record.action.description && <MarkdownRenderer value={record.action.description} />}
							{entities.map((entity) => {
								const previousVersion = history
									.slice(actionIndex + 1)
									.find(({ entities }) => entities[key] && entities[key].findIndex((e) => e.id === entity.id) !== -1);
								const previousEntities = (previousVersion?.entities[key] ?? []) as { id: string }[];
								const previous = previousEntities.find((e) => e.id === entity.id);
								return <Component key={entity.id} current={entity} previous={previous} />;
							})}
						</Stack>
					);
				})}
			</HistoryItem>
		);
	});
};
