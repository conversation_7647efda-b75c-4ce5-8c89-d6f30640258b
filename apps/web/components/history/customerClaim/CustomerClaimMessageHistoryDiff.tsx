import type { customerClaimMessage } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import type { z } from 'zod';
import { type KeyDiffMap, KeyDiffViewer } from '../KeyDiffViewer';
import { MarkdownRenderer } from '../MarkdownRenderer';

type Props = {
	current?: z.infer<typeof customerClaimMessage>;
	previous?: z.infer<typeof customerClaimMessage>;
};

const keyDiffMap: KeyDiffMap<z.infer<typeof customerClaimMessage>> = {
	message: {
		label: 'Zpráva',
		formatter: (value) => <MarkdownRenderer value={value as string} />,
	},
};

export const CustomerClaimMessageHistoryDiff: FC<Props> = (props) => {
	return <KeyDiffViewer keyDiffMap={keyDiffMap} {...props} />;
};
