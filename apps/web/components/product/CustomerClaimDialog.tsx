import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>le, DialogTrigger, Icon, useDialog } from '@pocitarna-nx-2023/ui';
import type { ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { useRouter } from 'next/router';
import { type FC } from 'react';
import { CustomerClaimSubmissionForm } from '../customerClaim/CustomerClaimSubmissionForm';

type Props = {
	product: ApiBody<'getProduct'>;
	variant?: 'icon-only';
};

export const CustomerClaimDialog: FC<Props> = ({ product, variant }) => {
	const { data: orderData } = apiHooks.useGetProductOrder({ params: { productId: product.id } });
	const order = orderData?._data;
	const orderItem: ApiBody<'getEcommerceOrder'>['items'][number] | undefined = order?.items.find((item) => item.productId === product.id);
	const { closeDialog } = useDialog();
	const router = useRouter();
	const isIconOnly = variant === 'icon-only';

	const { mutate: createCustomerClaim, isLoading } = apiHooks.useCreateCustomerClaim();
	const { invalidate: invalidateProductDefects } = apiHooks.useGetProductDefects(
		{
			params: { productId: product.id },
		},
		{ enabled: false },
	);
	const { invalidate: invalidateCustomerClaims } = apiHooks.useGetCustomerClaims({}, { enabled: false });
	const { invalidate: invalidateProducts } = apiHooks.useGetAllProducts({}, { enabled: false });
	const { invalidate: invalidateProduct } = apiHooks.useGetProduct({ params: { productId: product.id } }, { enabled: false });

	if (!order || !orderItem) return null;

	const trigger = (
		<Button variant="secondary" width={isIconOnly ? 'icon' : undefined}>
			<Icon name="bullhorn" />
			{!isIconOnly && 'Nová zákaznická reklamace'}
		</Button>
	);

	return (
		<Dialog>
			<DialogTrigger asChild>{trigger}</DialogTrigger>
			<DialogContent size="lg">
				<DialogHeader>
					<DialogTitle>Nová zákaznická reklamace</DialogTitle>
				</DialogHeader>
				<CustomerClaimSubmissionForm
					order={order}
					email={order.contact?.email}
					ecommerceOrderItemId={orderItem.id}
					isLoading={isLoading}
					onSubmit={(data) => {
						createCustomerClaim(data, {
							onSuccess: (data) => {
								closeDialog();
								invalidateProducts();
								invalidateProduct();
								invalidateCustomerClaims();
								invalidateProductDefects();
								router.push(`/customer-claim/${data._data}`);
							},
						});
					}}
				/>
			</DialogContent>
		</Dialog>
	);
};
