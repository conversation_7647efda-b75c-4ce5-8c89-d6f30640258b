import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, Stack } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { PdfAccompanyingSliplLink, PdfProtocolLink } from '../../customerClaim/PdfLinks';

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
};

export const CustomerClaimActions: FC<Props> = ({ customerClaim }) => {
	return (
		<Card sticky>
			<CardHeader>
				<CardTitle>Akce</CardTitle>
			</CardHeader>
			<CardContent>
				<Stack gap={4}>
					<PdfProtocolLink customerClaim={customerClaim} />
					<PdfAccompanyingSliplLink customerClaim={customerClaim} />
				</Stack>
			</CardContent>
		</Card>
	);
};
