import { type CustomerClaimFileType, CustomerClaimFileTypeMap } from '@pocitarna-nx-2023/config';
import { Card, CardContent, CardHeader, CardTitle, Media, ParamItem, ParamList, ParamSpacer } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC, Fragment, useMemo } from 'react';
import { useGroupCustomerClaimFiles } from '../../../hooks/useGroupCustomerClaimFiles';

type Props = {
	customerClaim: ApiBody<'getCustomerClaim'>;
};

type CustomerClaimFileGroup = { type: CustomerClaimFileType; files: ApiBody<'getCustomerClaimFiles'>[number]['file'][] };

export const CustomerClaimFiles: FC<Props> = ({ customerClaim }) => {
	const { data: customerClaimFilesData } = apiHooks.useGetPublicCustomerClaimFiles({
		params: { customerClaimId: customerClaim.id },
	});

	const customerClaimFiles = useMemo(() => customerClaimFilesData?._data ?? [], [customerClaimFilesData?._data]);

	const customerClaimFileGroups = useGroupCustomerClaimFiles({ customerClaimFiles });

	return (
		<Card>
			<CardHeader>
				<CardTitle>Soubory</CardTitle>
			</CardHeader>
			<CardContent>
				<ParamList lineBreak={true} className="empty:hidden">
					{customerClaimFileGroups.map(({ type, files }: CustomerClaimFileGroup, index) => (
						<Fragment key={type}>
							{index > 0 && <ParamSpacer lineBreak={true} />}
							<ParamItem label={CustomerClaimFileTypeMap[type]}>
								<Media files={files} />
							</ParamItem>
						</Fragment>
					))}
				</ParamList>
			</CardContent>
		</Card>
	);
};
