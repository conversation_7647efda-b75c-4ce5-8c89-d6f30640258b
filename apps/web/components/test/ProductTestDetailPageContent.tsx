import { Alert, AlertDescription, AlertTitle, Icon } from '@pocitarna-nx-2023/ui';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { type FC } from 'react';
import { useProductCosmeticDefectsFormData } from '../../hooks/useCosmeticDefectsFormData';
import { ProductFilesCard } from '../product/ProductFilesCard';
import { ProductDefectsManagement } from '../productDefect/ProductDefectsManagement';
import { TestDiff } from './TestDiff';
import { TestOverview } from './TestOverview';
import { TestSubmitButton } from './TestSubmitButton';

type Props = {
	product: ApiBody<'getProduct'>;
	productWasAlreadyTested: boolean;
	hasTestingRights: boolean;
};

export const ProductTestDetailPageContent: FC<Props> = ({ product, productWasAlreadyTested, hasTestingRights }) => {
	const minimumTestPhotosAmount = product.productCategory?.minimumTestPhotos ?? 0;
	const { allRelevantDefectsHaveImage } = useProductCosmeticDefectsFormData(product);

	if (!productWasAlreadyTested) {
		if (hasTestingRights) {
			return (
				<TestDiff product={product} minimumTestPhotosAmount={minimumTestPhotosAmount}>
					<ProductFilesCard
						product={product}
						description={
							<>
								Minimální počet obrázků pro úspěšné splnění testu: <strong>{minimumTestPhotosAmount}</strong>
							</>
						}
						disabled={!hasTestingRights}
					/>
					<ProductDefectsManagement product={product} source="TESTING" />
				</TestDiff>
			);
		} else {
			return (
				<Alert variant="info">
					<Icon name="circle-info" />
					<AlertTitle>Tento produkt ještě nebyl testován.</AlertTitle>
					<AlertDescription>Vraťte se na tuto stránku později, abyste viděli přehled výsledků testu.</AlertDescription>
				</Alert>
			);
		}
	}

	return (
		<>
			<TestOverview product={product} />
			<ProductFilesCard product={product} disabled={!hasTestingRights} />
			<TestSubmitButton
				product={product}
				minimumTestPhotosAmount={minimumTestPhotosAmount}
				allRelevantDefectsHaveImage={allRelevantDefectsHaveImage}
			/>
		</>
	);
};
