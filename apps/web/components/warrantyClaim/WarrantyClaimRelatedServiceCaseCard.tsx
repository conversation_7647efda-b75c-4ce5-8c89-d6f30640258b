import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ParamItem, ParamList } from '@pocitarna-nx-2023/ui';
import { formatServiceCaseCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import Link from 'next/link';
import { type FC } from 'react';

type Props = {
	warrantyClaimId: string;
};

export const WarrantyClaimRelatedServiceCaseCard: FC<Props> = ({ warrantyClaimId }) => {
	const { data: relatedClaimData } = apiHooks.useGetServiceCases({
		queries: {
			page: 1,
			limit: 1,
			filter: {
				'action.endpoint': {
					eq: `/warranty-claim/${warrantyClaimId}`,
				},
				'action.method': {
					eq: 'PATCH',
				},
			},
		},
	});

	const relatedCase = relatedClaimData?._data?.[0];

	if (!relatedCase) return null;

	return (
		<Card>
			<CardHeader className="flex flex-col justify-between items-start md:flex-row">
				<CardTitle>Servis</CardTitle>
			</CardHeader>
			<CardContent>
				<ParamList>
					<ParamItem label="Servis" align="center">
						<Link className="text-link" href={`/service/${relatedCase.id}`}>
							{formatServiceCaseCode(relatedCase.code)}
						</Link>
					</ParamItem>
				</ParamList>
			</CardContent>
		</Card>
	);
};
