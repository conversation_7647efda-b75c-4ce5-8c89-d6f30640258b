import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, ParamItem, ParamList } from '@pocitarna-nx-2023/ui';
import { formatWarrantyClaimCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import Link from 'next/link';
import { type FC } from 'react';

type Props = {
	relatedWarrantyClaim: ApiBody<'getWarrantyClaim'>;
};

export const RelatedWarrantyClaimCard: FC<Props> = ({ relatedWarrantyClaim }) => {
	return (
		<Card>
			<CardHeader className="flex flex-col justify-between items-start md:flex-row">
				<CardTitle>Dod. reklamace</CardTitle>
			</CardHeader>
			<CardContent>
				<ParamList>
					<ParamItem label="Reklamace" align="center">
						<Link className="text-link" href={`/warranty-claim/${relatedWarrantyClaim.id}`}>
							{formatWarrantyClaimCode(relatedWarrantyClaim.code)}
						</Link>
					</ParamItem>
				</ParamList>
			</CardContent>
		</Card>
	);
};
