import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Param<PERSON>tem, ParamList } from '@pocitarna-nx-2023/ui';
import { formatServiceCaseCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import Link from 'next/link';
import { type FC } from 'react';

type Props = {
	relatedServiceCase: ApiBody<'getServiceCase'>;
};

export const RelatedServiceCaseCard: FC<Props> = ({ relatedServiceCase }) => {
	return (
		<Card>
			<CardHeader className="flex flex-col justify-between items-start md:flex-row">
				<CardTitle>Servis</CardTitle>
			</CardHeader>
			<CardContent>
				<ParamList>
					<ParamItem label="Servis" align="center">
						<Link className="text-link" href={`/service/${relatedServiceCase.id}`}>
							{formatServiceCaseCode(relatedServiceCase.code)}
						</Link>
					</ParamItem>
				</ParamList>
			</CardContent>
		</Card>
	);
};
