import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type FC } from 'react';
import { RelatedWarrantyClaimCard } from '../warrantyClaim/RelatedWarrantyClaimCard';

type Props = {
	serviceCaseId: string;
};

export const ServiceCaseRelatedWarrantyClaimCard: FC<Props> = ({ serviceCaseId }) => {
	const { data: relatedClaimData } = apiHooks.useGetWarrantyClaims({
		queries: {
			page: 1,
			limit: 1,
			filter: {
				'action.endpoint': {
					eq: `/service-case/${serviceCaseId}`,
				},
				'action.method': {
					eq: 'PATCH',
				},
			},
		},
	});

	const relatedClaim = relatedClaimData?._data?.[0];

	if (!relatedClaim) return null;

	return <RelatedWarrantyClaimCard relatedWarrantyClaim={relatedClaim} />;
};
