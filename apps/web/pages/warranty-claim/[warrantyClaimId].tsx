import { type WarrantyClaimStatus, WarrantyClaimStatusMessage } from '@pocitarna-nx-2023/config';
import { Badge, Container, SidebarGrid, Stack, Title } from '@pocitarna-nx-2023/ui';
import { formatWarrantyClaimCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps } from 'next';
import { type FC } from 'react';
import { RelatedCustomerClaimCard } from '../../components/customerClaim/RelatedCustomerClaimCard';
import { HistoryAccordion } from '../../components/history/HistoryAccordion';
import { WarrantyClaimHistory } from '../../components/history/warrantyClaim/WarrantyClaimHistory';
import { NotesManagement } from '../../components/NotesManagement';
import { ProductPlacementInfo } from '../../components/product/ProductPlacementInfo';
import { ProductPriceOverview } from '../../components/product/ProductPriceOverview';
import { ProductDefectsManagement } from '../../components/productDefect/ProductDefectsManagement';
import { WarrantyClaimDefectSolver } from '../../components/warrantyClaim/WarrantyClaimDefectSolver';
import { WarrantyClaimFilesCard } from '../../components/warrantyClaim/WarrantyClaimFilesCard';
import { WarrantyClaimRelatedServiceCaseCard } from '../../components/warrantyClaim/WarrantyClaimRelatedServiceCaseCard';
import { WarrantyClaimStatusButtons } from '../../components/warrantyClaim/WarrantyClaimStatusButtons';
import { WarrantyClaimTrackingCard } from '../../components/warrantyClaim/WarrantyClaimTrackingCard';
import { WarrantyClaimTransitionHandler } from '../../components/warrantyClaim/warrantyClaimTransitionHandler/WarrantyClaimTransitionHandler';
import { WarrantyClaimVendorOverview } from '../../components/warrantyClaim/WarrantyClaimVendorOverview';
import { useUserHasScope } from '../../hooks/useUserHasScope';
import { useWarrantyClaimData } from '../../hooks/useWarrantyClaimData';
import { checkScope } from '../../utils/checkScope';

type Props = { warrantyClaimId: string };

const WarrantyClaim: FC<Props> = ({ warrantyClaimId }) => {
	const { data: warrantyClaimData, invalidate } = apiHooks.useGetWarrantyClaim(
		{ params: { warrantyClaimId } },
		{ enabled: Boolean(warrantyClaimId) },
	);

	const { invalidate: invalidateWarrantyClaimHistory } = apiHooks.useGetWarrantyClaimHistory(
		{ params: { warrantyClaimId } },
		{ enabled: false },
	);

	const warrantyClaim = warrantyClaimData?._data;
	const { product, vendorCoveredProductDefects, vendorUncoveredDefectTypes } = useWarrantyClaimData(warrantyClaimId);
	const hasWarrantyClaimWriteRights = useUserHasScope('warrantyClaimWrite');

	if (!warrantyClaim || !product) return null;

	return (
		<Container>
			<Stack>
				<Title
					title={`Dod. reklamace ${formatWarrantyClaimCode(warrantyClaim.code)}`}
					backlink={{ url: '/warranty-claim', label: 'Dod. reklamace' }}
					titleChildren={<Badge variant="info">{WarrantyClaimStatusMessage[warrantyClaim.status as WarrantyClaimStatus]}</Badge>}
				/>
				<SidebarGrid>
					<Stack>
						{product.status === 'MISSING' && <p></p>}
						<ProductDefectsManagement
							product={product}
							vendorUncoveredDefectTypes={vendorUncoveredDefectTypes}
							warrantyClaimId={warrantyClaimId}
							disabled={!hasWarrantyClaimWriteRights || warrantyClaim.status === 'CLOSED'}
							source="WARRANTY_CLAIM"
							invalidate={invalidateWarrantyClaimHistory}
						/>

						<WarrantyClaimFilesCard warrantyClaim={warrantyClaim} disabled={!hasWarrantyClaimWriteRights} />

						<HistoryAccordion defaultOpen>
							<WarrantyClaimHistory warrantyClaim={warrantyClaim}>
								{['REJECTED', 'RESOLVED_BY_VENDOR'].includes(warrantyClaim.status) ? (
									<WarrantyClaimDefectSolver
										productDefects={vendorCoveredProductDefects}
										warrantyClaimId={warrantyClaim.id}
										disabled={!hasWarrantyClaimWriteRights}
									/>
								) : (
									<WarrantyClaimTransitionHandler
										warrantyClaim={warrantyClaim}
										productDefects={vendorCoveredProductDefects}
										product={product}
										disabled={!hasWarrantyClaimWriteRights}
									/>
								)}
							</WarrantyClaimHistory>
						</HistoryAccordion>
					</Stack>
					<Stack>
						<ProductPlacementInfo product={product} />
						<ProductPriceOverview product={product} />
						<NotesManagement
							entity={warrantyClaim}
							entityType="warrantyClaim"
							invalidate={invalidate}
							disabled={!hasWarrantyClaimWriteRights}
						/>
						<WarrantyClaimTrackingCard
							warrantyClaim={warrantyClaim}
							disabled={!hasWarrantyClaimWriteRights}
							invalidate={invalidate}
						/>
						<WarrantyClaimRelatedServiceCaseCard warrantyClaimId={warrantyClaimId} />
						<RelatedCustomerClaimCard relatedCustomerClaimId={warrantyClaim.sourceCustomerClaimId} />
						<WarrantyClaimVendorOverview product={product} />
						<WarrantyClaimStatusButtons warrantyClaim={warrantyClaim} product={product} />
					</Stack>
				</SidebarGrid>
			</Stack>
		</Container>
	);
};

export default WarrantyClaim;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	const warrantyClaimId = ctx.params?.warrantyClaimId;

	return {
		props: {
			warrantyClaimId:
				typeof warrantyClaimId === 'string' ? warrantyClaimId : Array.isArray(warrantyClaimId) ? warrantyClaimId[0] : '',
		},
		redirect: await checkScope(ctx, 'warrantyClaimRead'),
	};
};
