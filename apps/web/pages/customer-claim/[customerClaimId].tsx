import { CustomerClaimHandlingMethodMessage, CustomerDeliveryMethodMessage, TEN_SECONDS } from '@pocitarna-nx-2023/config';
import {
	Card,
	CardContent,
	CardHeader,
	MutedText,
	Page,
	PageTitle,
	ParamItem,
	ParamList,
	SidebarGrid,
	Stack,
	Title,
	toast,
} from '@pocitarna-nx-2023/ui';
import { formatCustomerClaimCode, formatDateTime, formatProductCode } from '@pocitarna-nx-2023/utils';
import { type ApiBody } from '@pocitarna-nx-2023/zodios';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps } from 'next';
import { type FC, useMemo } from 'react';
import { AddressCard } from '../../components/AddressCard';
import { AcceptanceCard } from '../../components/customerClaim/AcceptanceCard';
import { CustomerClaimActions } from '../../components/customerClaim/CustomerClaimActions';
import { CustomerClaimBadge } from '../../components/customerClaim/CustomerClaimBadge';
import { CustomerClaimCountdownCard } from '../../components/customerClaim/CustomerClaimCountdown';
import { CustomerClaimCreationFiles } from '../../components/customerClaim/CustomerClaimCreationFiles';
import { CustomerClaimFilesCard } from '../../components/customerClaim/CustomerClaimFilesCard';
import { DefectsSection } from '../../components/customerClaim/DefectsSection';
import { CustomerClaimFlowHandler } from '../../components/customerClaim/flows/CustomerClaimFlowHandler';
import { OrderCard } from '../../components/customerClaim/OrderCard';
import { CustomerClaimHistory } from '../../components/history/customerClaim/CustomerClaimHistory';
import { HistoryAccordion } from '../../components/history/HistoryAccordion';
import { NotesManagement } from '../../components/NotesManagement';
import { ProductPlacementInfo } from '../../components/product/ProductPlacementInfo';
import { ProductPriceOverview } from '../../components/product/ProductPriceOverview';
import { ProductDefectsManagement } from '../../components/productDefect/ProductDefectsManagement';
import { RelatedServiceCaseCard } from '../../components/service/RelatedServiceCaseCard';
import { RelatedWarrantyClaimCard } from '../../components/warrantyClaim/RelatedWarrantyClaimCard';
import { useUserHasScope } from '../../hooks/useUserHasScope';
import { checkScope } from '../../utils/checkScope';

type Props = { customerClaimId: string };

const CustomerClaim: FC<Props> = ({ customerClaimId }) => {
	const { data: customerClaimData, invalidate } = apiHooks.useGetCustomerClaim(
		{ params: { customerClaimId } },
		{ enabled: Boolean(customerClaimId) },
	);
	const customerClaim = useMemo(() => {
		return customerClaimData?._data;
	}, [customerClaimData?._data]);

	if (!customerClaim) return null;

	return <PageContent customerClaim={customerClaim} invalidate={invalidate} />;
};

const PageContent: FC<{ customerClaim: ApiBody<'getCustomerClaim'>; invalidate: () => Promise<void> }> = ({
	customerClaim,
	invalidate,
}) => {
	const orderItem = customerClaim.ecommerceOrderItem;
	const { data: productData } = apiHooks.useGetProduct(
		{ params: { productId: orderItem.productId ?? '' } },
		{ enabled: Boolean(orderItem.productId) },
	);
	const { data: customerClaimFilesData } = apiHooks.useGetCustomerClaimFiles(
		{
			params: { customerClaimId: customerClaim.id },
		},
		{ refetchInterval: TEN_SECONDS },
	);
	const { invalidate: invalidateCustomerClaims } = apiHooks.useGetCustomerClaims({}, { enabled: false });
	const product = productData?._data;
	const customerClaimFiles = customerClaimFilesData?._data ?? [];
	const title = `Reklamace č. ${formatCustomerClaimCode(customerClaim.code)}`;
	const customerMessages = customerClaim.messages.filter((message) => message.contactId !== null);
	const hasCustomerClaimWriteRights = useUserHasScope('customerClaimWrite');
	const relatedWarrantyClaims = customerClaim.warrantyClaims;
	const relatedServiceCases = customerClaim.serviceCases;

	if (!product) return null;

	const onClaimUpdate = () => {
		invalidate();
		invalidateCustomerClaims();
		toast.success('Zaktualizováno');
	};

	return (
		<>
			<PageTitle title={title} />

			<Page className="min-h-full p-4 justify-center flex flex-col md:p-10">
				<Stack>
					<Title
						title={title}
						backlink={{ label: 'Reklamace', url: '/customer-claim' }}
						titleChildren={
							<>
								<MutedText>{formatDateTime(customerClaim.createdAt)}</MutedText>

								<CustomerClaimBadge customerClaim={customerClaim} />
							</>
						}
					/>

					<SidebarGrid>
						<Stack>
							<Card>
								<CardHeader>
									<h3 className="font-semibold leading-none tracking-tight">Hlášení reklamace</h3>
								</CardHeader>
								<CardContent>
									<Stack>
										<Stack gap={1}>
											<h5 className="font-semibold leading-none tracking-tight">{orderItem.name}</h5>
											<MutedText>
												{formatProductCode(product.code)}
												<br />
												{product?.sn?.toUpperCase()}
											</MutedText>
										</Stack>
										<ParamList>
											<ParamItem label="Popis závady">
												{customerMessages.map((message) => message.message).join('\n')}
											</ParamItem>
											<ParamItem label="Preferovaný způsob vyřízení reklamace">
												{CustomerClaimHandlingMethodMessage[customerClaim.handlingMethod]}
											</ParamItem>
											<ParamItem label="Způsob doručení">
												{CustomerDeliveryMethodMessage[customerClaim.customerDeliveryMethod]}
											</ParamItem>
										</ParamList>
										<CustomerClaimCreationFiles files={customerClaimFiles} />
									</Stack>
								</CardContent>
							</Card>
							<CustomerClaimFilesCard customerClaim={customerClaim} customerClaimFiles={customerClaimFiles} />
							<ProductDefectsManagement product={product} source="CUSTOMER_CLAIM" customerClaimId={customerClaim.id} />
							<DefectsSection customerClaim={customerClaim} product={product} />
						</Stack>
						<Stack>
							{customerClaim.receivedAt && <CustomerClaimCountdownCard customerClaim={customerClaim} />}
							{customerClaim.status !== 'NEW' && (
								<AcceptanceCard customerClaim={customerClaim} onClaimUpdate={onClaimUpdate} />
							)}
							<CustomerClaimActions customerClaim={customerClaim} />
							<OrderCard customerClaim={customerClaim} />
							<AddressCard invoiceAddress={customerClaim.address} contact={customerClaim.contact} />
							<ProductPlacementInfo product={product} />
							<ProductPriceOverview product={product} />
							<NotesManagement
								entity={customerClaim}
								entityType="customerClaim"
								invalidate={invalidate}
								disabled={!hasCustomerClaimWriteRights}
							/>
							{relatedWarrantyClaims.length > 0 &&
								relatedWarrantyClaims.map((relatedWarrantyClaim) => (
									<RelatedWarrantyClaimCard key={relatedWarrantyClaim.id} relatedWarrantyClaim={relatedWarrantyClaim} />
								))}
							{relatedServiceCases.length > 0 &&
								relatedServiceCases.map((relatedServiceCase) => (
									<RelatedServiceCaseCard key={relatedServiceCase.id} relatedServiceCase={relatedServiceCase} />
								))}
						</Stack>
					</SidebarGrid>
					<HistoryAccordion defaultOpen>
						<CustomerClaimHistory customerClaim={customerClaim}>
							<CustomerClaimFlowHandler
								product={product}
								customerClaim={customerClaim}
								disabled={!hasCustomerClaimWriteRights}
								onClaimUpdate={onClaimUpdate}
							/>
						</CustomerClaimHistory>
					</HistoryAccordion>
				</Stack>
			</Page>
		</>
	);
};

export default CustomerClaim;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	const customerClaimId = ctx.params?.customerClaimId;

	return {
		props: {
			customerClaimId:
				typeof customerClaimId === 'string' ? customerClaimId : Array.isArray(customerClaimId) ? customerClaimId[0] : '',
		},
		redirect: await checkScope(ctx, 'customerClaimRead'),
	};
};
