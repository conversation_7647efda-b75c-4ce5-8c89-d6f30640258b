import { ServiceCaseStatusMessage, ServiceCaseTypeMessage } from '@pocitarna-nx-2023/config';
import { Badge, Container, SidebarGrid, Stack, Title } from '@pocitarna-nx-2023/ui';
import { formatServiceCaseCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps } from 'next';
import { type FC, Fragment } from 'react';
import { RelatedCustomerClaimCard } from '../../components/customerClaim/RelatedCustomerClaimCard';
import { HistoryAccordion } from '../../components/history/HistoryAccordion';
import { ServiceCaseHistory } from '../../components/history/serviceCase/ServiceCaseHistory';
import { NotesManagement } from '../../components/NotesManagement';
import { ProductPlacementInfo } from '../../components/product/ProductPlacementInfo';
import { ProductPriceOverview } from '../../components/product/ProductPriceOverview';
import { ProductDefectsManagement } from '../../components/productDefect/ProductDefectsManagement';
import { InternalServiceCaseHandler } from '../../components/service/internalService/InternalServiceCaseHandler';
import { ServiceCaseFilesCard } from '../../components/service/ServiceCaseFilesCard';
import { ServiceCasePrints } from '../../components/service/ServiceCasePrints';
import { ServiceCaseRelatedWarrantyClaimCard } from '../../components/service/ServiceCaseRelatedWarrantyClaimCard';
import { ServiceCaseStatusButtons } from '../../components/service/ServiceCaseStatusButtons';
import { ServiceCaseTrackingCard } from '../../components/service/ServiceCaseTrackingCard';
import { ServiceCaseTransitionHandler } from '../../components/service/serviceCaseTransitionHandler/ServiceCaseTransitionHandler';
import { PriorityDisplay } from '../../components/test/PriorityDisplay';
import { useServiceCaseData } from '../../hooks/useServiceCaseData';
import { useUserHasScope } from '../../hooks/useUserHasScope';
import { checkScope } from '../../utils/checkScope';

// FIXME: this whole page relies on the fact that there is ONE product associated to the service case (especially components: external ServiceCaseTransitionHandler, ServiceCasePrints, ServiceCaseStatusButtons).
// For now I'm just showing multiple product-info cards if there are multiple products, but should the whole logic be altered to accommodate the order service-case logic, since probably this flow will change in the future?

type Props = { serviceCaseId: string };

const ServiceCase: FC<Props> = ({ serviceCaseId }) => {
	const { data: serviceCaseData, invalidate } = apiHooks.useGetServiceCase(
		{ params: { serviceCaseId } },
		{ enabled: Boolean(serviceCaseId) },
	);
	const serviceCase = serviceCaseData?._data;
	const { product, productDefects, allAffectedProducts } = useServiceCaseData(serviceCaseId, 'all-affected-products');
	const { invalidate: invalidateServiceCaseHistory } = apiHooks.useGetServiceCaseHistory(
		{ params: { serviceCaseId } },
		{ enabled: false },
	);

	const assignedServiceCenterId = serviceCase?.serviceCenterId;
	const hasServiceWriteRights = useUserHasScope('serviceWrite');

	if (!serviceCase || !product) return null;

	return (
		<Container>
			<Stack>
				<Title
					title={`Servis ${formatServiceCaseCode(serviceCase.code)}`}
					backlink={{ url: '/service', label: ' Servisy' }}
					titleChildren={
						<>
							<Badge variant="info">{ServiceCaseStatusMessage[serviceCase.status]}</Badge>
							<Badge variant="secondary">
								<PriorityDisplay entity={serviceCase} />
							</Badge>
							<Badge variant="outline">{ServiceCaseTypeMessage[serviceCase.type]}</Badge>
						</>
					}
				/>
				<SidebarGrid>
					<Stack>
						<ProductDefectsManagement
							product={product}
							serviceCaseId={serviceCaseId}
							disabled={!hasServiceWriteRights || serviceCase.status === 'CLOSED'}
							source="SERVICE_CASE"
							invalidate={invalidateServiceCaseHistory}
						/>
						<ServiceCaseFilesCard serviceCase={serviceCase} disabled={!hasServiceWriteRights} />

						<HistoryAccordion defaultOpen>
							<ServiceCaseHistory serviceCase={serviceCase}>
								{serviceCase?.status === 'ASSIGNED_TO_INTERNAL_SERVICE' ? (
									<InternalServiceCaseHandler
										productDefects={productDefects}
										serviceCase={serviceCase}
										disabled={!hasServiceWriteRights}
									/>
								) : (
									<ServiceCaseTransitionHandler
										serviceCase={serviceCase}
										productId={product.id}
										productDefects={productDefects}
										assignedServiceCenterId={assignedServiceCenterId}
										disabled={!hasServiceWriteRights}
									/>
								)}
							</ServiceCaseHistory>
						</HistoryAccordion>
					</Stack>
					<Stack>
						{allAffectedProducts.map((product) => (
							<Fragment key={product.id}>
								<ProductPlacementInfo product={product} />
								<ProductPriceOverview product={product} />
							</Fragment>
						))}
						<NotesManagement
							entity={serviceCase}
							entityType="serviceCase"
							invalidate={invalidate}
							disabled={!hasServiceWriteRights}
						/>
						<ServiceCaseTrackingCard serviceCase={serviceCase} disabled={!hasServiceWriteRights} invalidate={invalidate} />
						<ServiceCaseRelatedWarrantyClaimCard serviceCaseId={serviceCaseId} />
						<RelatedCustomerClaimCard relatedCustomerClaimId={serviceCase.sourceCustomerClaimId} />
						<ServiceCasePrints serviceCase={serviceCase} product={product} />
						<ServiceCaseStatusButtons serviceCase={serviceCase} product={product} />
					</Stack>
				</SidebarGrid>
			</Stack>
		</Container>
	);
};

export default ServiceCase;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	const serviceCaseId = ctx.params?.serviceCaseId;

	return {
		props: {
			serviceCaseId: typeof serviceCaseId === 'string' ? serviceCaseId : Array.isArray(serviceCaseId) ? serviceCaseId[0] : '',
		},
		redirect: await checkScope(ctx, 'serviceRead'),
	};
};
