import { CustomerClaimHandlingMethodMessage, CustomerDeliveryMethodMessage, MAX_POSITIVE_INTEGER } from '@pocitarna-nx-2023/config';
import { Card, CardContent, CardHeader, MutedText, ParamItem, ParamList, SidebarGrid, Stack, Title } from '@pocitarna-nx-2023/ui';
import { formatCustomerClaimCode, formatDateTime, formatProductCode } from '@pocitarna-nx-2023/utils';
import { apiHooks } from '@pocitarna-nx-2023/zodios-client';
import { type GetServerSideProps } from 'next';
import { type ReactElement } from 'react';
import { CustomerClaimBadge } from '../../../components/customerClaim/CustomerClaimBadge';
import { CustomerClaimCreationFiles } from '../../../components/customerClaim/CustomerClaimCreationFiles';
import { OrderCard } from '../../../components/customerClaim/OrderCard';
import { PublicCustomerClaimHistory } from '../../../components/history/customerClaim/PublicCustomerClaimHistory';
import { HistoryAccordion } from '../../../components/history/HistoryAccordion';
import { CustomerClaimActions } from '../../../components/public/customerClaim/CustomerClaimActions';
import { CustomerClaimFiles } from '../../../components/public/customerClaim/CustomerClaimFiles';
import { PublicLayout } from '../../../components/public/Layout';
import { type NextPageWithLayout } from '../../../types/next';

type Props = { customerClaimId: string };

const PublicCustomerClaim: NextPageWithLayout<Props> = ({ customerClaimId }) => {
	const { data } = apiHooks.useGetPublicCustomerClaim({ params: { customerClaimId } });
	const customerClaim = data?._data ?? null;
	const orderItem = customerClaim?.ecommerceOrderItem;

	const { data: productData } = apiHooks.useGetPublicProduct(
		{ params: { productId: orderItem?.productId ?? '' } },
		{ enabled: Boolean(orderItem?.productId) },
	);
	const product = productData?._data;

	const { data: filesData } = apiHooks.useGetPublicCustomerClaimFiles({
		params: { customerClaimId },
		queries: { page: 1, limit: MAX_POSITIVE_INTEGER },
	});

	const files = filesData?._data ?? [];

	if (!customerClaim || !product || !orderItem) return null;

	const title = `Reklamace č. ${formatCustomerClaimCode(customerClaim.code)}`;
	const customerMessages = customerClaim.messages.filter((message) => message.contactId !== null);

	return (
		<Stack>
			<Title
				title={title}
				backlink={{ label: 'Reklamace' }}
				titleChildren={
					<>
						<MutedText>{formatDateTime(customerClaim.createdAt)}</MutedText>
						<CustomerClaimBadge customerClaim={customerClaim} />
					</>
				}
			/>

			<SidebarGrid>
				<Stack>
					<Card>
						<CardHeader>
							<h3 className="font-semibold leading-none tracking-tight">Hlášení reklamace</h3>
						</CardHeader>
						<CardContent>
							<Stack>
								<Stack gap={1}>
									<h5 className="font-semibold leading-none tracking-tight">{orderItem.name}</h5>
									<MutedText>
										{formatProductCode(product.code)}
										<br />
										{product?.sn?.toUpperCase()}
									</MutedText>
								</Stack>
								<ParamList>
									<ParamItem label="Popis závady">
										{customerMessages.map((message) => message.message).join('\n')}
									</ParamItem>
									<ParamItem label="Preferovaný způsob vyřízení reklamace">
										{CustomerClaimHandlingMethodMessage[customerClaim.handlingMethod]}
									</ParamItem>
									<ParamItem label="Způsob doručení">
										{CustomerDeliveryMethodMessage[customerClaim.customerDeliveryMethod]}
									</ParamItem>
								</ParamList>
								<CustomerClaimCreationFiles files={files} />
							</Stack>
						</CardContent>
					</Card>
					<CustomerClaimFiles customerClaim={customerClaim} />
				</Stack>
				<Stack>
					<CustomerClaimActions customerClaim={customerClaim} />
					<OrderCard customerClaim={customerClaim} />
				</Stack>
			</SidebarGrid>
			<HistoryAccordion defaultOpen>
				<PublicCustomerClaimHistory customerClaim={customerClaim} />
			</HistoryAccordion>
		</Stack>
	);
};

export default PublicCustomerClaim;

PublicCustomerClaim.getLayout = (page: ReactElement) => {
	return <PublicLayout>{page}</PublicLayout>;
};

export const getServerSideProps: GetServerSideProps = async (ctx) => {
	const customerClaimId = ctx.params?.customerClaimId;

	return {
		props: {
			customerClaimId:
				typeof customerClaimId === 'string' ? customerClaimId : Array.isArray(customerClaimId) ? customerClaimId[0] : '',
		},
	};
};
