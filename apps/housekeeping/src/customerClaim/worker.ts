import { triggerEmail, withSentry } from '@pocitarna-nx-2023/aws';
import { CUSTOMER_CLAIM_CANCELLATION_DAYS, CUSTOMER_CLAIM_WARNING_DAYS } from '@pocitarna-nx-2023/config';
import { CustomerClaimController, listAll, withDatabase } from '@pocitarna-nx-2023/database';
import { formatCustomerClaimCode } from '@pocitarna-nx-2023/utils';
import { endOfDay, startOfDay, subDays } from 'date-fns';

export const handler = withSentry(
	'https://<EMAIL>/4509666423930960',
	withDatabase(async () => {
		const now = new Date();
		const warningPeriod = subDays(now, CUSTOMER_CLAIM_WARNING_DAYS);
		const cancellationPeriod = subDays(now, CUSTOMER_CLAIM_CANCELLATION_DAYS);

		const [claimsToWarnAbout] = await new CustomerClaimController().list(
			listAll({
				filter: {
					status: { eq: 'NEW' },
					createdAt: {
						gte: startOfDay(warningPeriod),
						lte: endOfDay(warningPeriod),
					},
				},
			}),
		);

		if (claimsToWarnAbout.length > 0) {
			await Promise.all(
				claimsToWarnAbout.map(async (claim) => {
					await triggerEmail({
						recipients: [claim.contact.email],
						subject: `Reklamace blížící se ke zrušení`,
						message: `<p>Dobrý den,</p><p>Vaše reklamace ${formatCustomerClaimCode(claim.code)} je blížící se ke zrušení.</p>
					<p>Máte ještě ${CUSTOMER_CLAIM_CANCELLATION_DAYS - CUSTOMER_CLAIM_WARNING_DAYS} dní na zaslání dotčeného produktu zpět.</p>`,
					});
				}),
			);
		}

		const [claimsToCancel] = await new CustomerClaimController().list(
			listAll({
				filter: {
					status: { eq: 'NEW' },
					createdAt: {
						lte: cancellationPeriod,
					},
				},
			}),
		);

		if (claimsToCancel.length > 0) {
			await Promise.all(
				claimsToCancel.map(async (claim) => {
					await triggerEmail({
						recipients: [claim.contact.email],
						subject: `Zrušení reklamace`,
						message: `<p>Dobrý den,</p><p>Vaše reklamace ${formatCustomerClaimCode(claim.code)} byla zrušena.</p>`,
					});
					await new CustomerClaimController().update(claim.id, { status: 'CANCELLED' });
				}),
			);
		}
	}),
);
