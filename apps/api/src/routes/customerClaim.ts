import { triggerEmail } from '@pocitarna-nx-2023/aws';
import {
	CustomerClaimCodeController,
	CustomerClaimController,
	CustomerClaimCosmeticDefectController,
	CustomerClaimMessageController,
	FileCustomerClaimController,
	listAll,
	ProductCategoryController,
	ProductController,
	ProductDefectController,
	ServiceCaseController,
	useAuthentication,
	WarrantyClaimController,
} from '@pocitarna-nx-2023/database';
import { generateCustomerClaimProtocol, getCustomerClaimProtocolLink } from '@pocitarna-nx-2023/pdf';
import { formatCustomerClaimCode } from '@pocitarna-nx-2023/utils';
import { customerClaimApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const customerClaimRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(customerClaimApi);

	router.post('/', scopeMiddleware('customerClaimWrite'), async (req) => {
		const customerClaim = await new CustomerClaimController().create(req.body);
		respond<'createCustomerClaim'>(customerClaim.id);
	});

	router.get('/', scopeMiddleware('customerClaimRead'), async () => {
		const [data, getCount] = await new CustomerClaimController().list(getListProps());

		return respondWithPaging<'getCustomerClaims'>(data, await getCount());
	});

	router.get('/code/:customerClaimCodeId', scopeMiddleware('customerClaimRead'), async (req, res) => {
		const customerClaimCode = await new CustomerClaimCodeController().findById(req.params.customerClaimCodeId);
		if (!customerClaimCode) return res.status(404).json();
		respond<'getCustomerClaimCode'>(customerClaimCode);
	});

	router.get('/:customerClaimId/protocol', async (req, res) => {
		const file = await generateCustomerClaimProtocol(req.params.customerClaimId);
		res.setHeader('Content-Type', 'application/pdf');
		res.setHeader('Content-Disposition', `attachment; filename=customer-claim-protocol-${new Date().toISOString()}.pdf`);
		res.send(file);
	});

	router.get('/:customerClaimId/accompanying-slip', async (req, res) => {
		const file = await generateCustomerClaimProtocol(req.params.customerClaimId);
		res.setHeader('Content-Type', 'application/pdf');
		res.setHeader('Content-Disposition', `attachment; filename=customer-claim-accompanying-slip-${new Date().toISOString()}.pdf`);
		res.send(file);
	});

	router.get('/:customerClaimId', scopeMiddleware('customerClaimRead'), async (req, res) => {
		const customerClaim = await new CustomerClaimController().findById(req.params.customerClaimId);
		if (!customerClaim) return res.status(404).json();
		respond<'getCustomerClaim'>(customerClaim);
	});

	router.patch('/:customerClaimId', scopeMiddleware('customerClaimWrite'), async (req) => {
		await new CustomerClaimController().update(req.params.customerClaimId, req.body);
		const { status, receivedAt } = req.body;
		const customerClaim = await new CustomerClaimController().resolveRecord(req.params.customerClaimId);
		const productId = customerClaim.ecommerceOrderItem?.productId;

		if (!productId) throw new Error('No product');

		if (status === 'RECEIVED' && receivedAt) {
			const categoryId = customerClaim.ecommerceOrderItem?.product?.productCategoryId;
			const productCategory = categoryId ? await new ProductCategoryController().findById(categoryId) : null;

			if (!productCategory) throw new Error('Product category not found');

			const categoryMinimumTestPhotosAmount = productCategory.minimumTestPhotos ?? 0;
			const [receiptCustomerClaimFiles] = await new FileCustomerClaimController().list(
				listAll({
					filter: {
						customerClaimId: { eq: req.params.customerClaimId },
						type: { eq: 'receipt' },
						'file.mime': { eq: '%image%' },
					},
				}),
			);

			if (receiptCustomerClaimFiles.length < categoryMinimumTestPhotosAmount) {
				throw new Error('Not enough images provided at receipt');
			}

			const [relatedCosmeticDefects] = await new CustomerClaimCosmeticDefectController().list(
				listAll({ filter: { customerClaimId: { eq: req.params.customerClaimId } } }),
			);
			const allCosmeticDefectsHaveImage = relatedCosmeticDefects.every((defect) => {
				if (!defect.cosmeticDefect?.pictureRequired) return true;
				return defect.files.length > 0;
			});

			if (!allCosmeticDefectsHaveImage) throw new Error('Not enough images provided for cosmetic defects');

			const pdfUrl = await getCustomerClaimProtocolLink(req.params.customerClaimId);
			const subject = `Protokol reklamace ${formatCustomerClaimCode(customerClaim.code)}`;
			const message = `<p>Dobrý den,</p><p>Poštovním dopravou Vám zasíláme protokol reklamace.</p>
				<p>Najděte svůj protokol <a href="${pdfUrl}">zde</a></p>`;

			// TODO: Check if all areas have defects or have been checked as clear, same as with products

			await triggerEmail({
				recipients: customerClaim.contact.email,
				subject,
				message,
			});
		}

		if (status === 'PROCESSING') {
			const [openClaimDefects] = await new ProductDefectController().list(
				listAll({
					filter: {
						productId: { eq: productId },
						customerClaimId: { eq: req.params.customerClaimId },
						serviceTaskId: { eq: null },
					},
				}),
			);

			if (openClaimDefects.length === 0) throw new Error('No open defects to process');

			// Redundant check, but better to run it also on the server
			const productWarranties = await new ProductController().getProductWarranties(productId);
			const buyWarranty = productWarranties.buyWarranty;
			const isInVendorWarranty = buyWarranty?.expiredAt && buyWarranty.expiredAt > new Date();

			if (isInVendorWarranty) {
				await new WarrantyClaimController().create({
					note: 'Založeno z zákaznické reklamace',
					productDefects: openClaimDefects,
					productId,
					sourceCustomerClaim: { id: req.params.customerClaimId },
				});
			} else {
				await new ServiceCaseController().create({
					note: 'Založeno z zákaznické reklamace',
					productDefects: openClaimDefects,
					productId,
					sourceCustomerClaim: { id: req.params.customerClaimId },
					type: 'FRONTEND',
				});
			}

			const subject = `Začali jsme pracovat na vaší reklamaci`;
			const message = `<p>Dobrý den,</p><p>Začali jsme pracovat na vaší zákaznické reklamaci ${formatCustomerClaimCode(customerClaim.code)}.</p>
				<p>Vaše reklamace byla přesunuta do systému servisních reklamací.</p>`;

			await triggerEmail({
				recipients: customerClaim.contact.email,
				subject,
				message,
			});
		}

		respond<'updateCustomerClaim'>(true);
	});

	router.patch('/:customerClaimId/files', scopeMiddleware('customerClaimWrite'), async (req) => {
		const fileIds = req.body;
		await new CustomerClaimController().addFiles(req.params.customerClaimId, fileIds, req.query.type);
		respond<'addFilesToCustomerClaim'>(true);
	});

	router.delete('/:customerClaimId/files/:fileId', scopeMiddleware('customerClaimWrite'), async (req, res) => {
		const entity = await new CustomerClaimController().deleteFile(req.params.customerClaimId, req.params.fileId);
		if (!entity) return res.status(500).send();

		respond<'deleteCustomerClaimFile'>(true);
	});

	router.post('/:customerClaimId/message', scopeMiddleware('customerClaimWrite'), async (req) => {
		const customerClaim = await new CustomerClaimController().resolveRecord(req.params.customerClaimId);

		await new CustomerClaimMessageController().create({
			message: req.body.message,
			customerClaim: { id: customerClaim.id },
			user: useAuthentication()?.user ?? null,
		});

		await triggerEmail({
			recipients: customerClaim.contact.email,
			subject: `Reklamace ${formatCustomerClaimCode(customerClaim.code)}`,
			message: req.body.message,
		});

		respond<'sendCustomerClaimMessage'>(true);
	});

	return router;
};
