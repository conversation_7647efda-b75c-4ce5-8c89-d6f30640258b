import {
	CosmeticDefect<PERSON>ontroller,
	CustomerClaimCosmeticDefectController,
	FileCustomerClaimCosmeticDefectController,
	FileProductCosmeticDefectController,
	ProductCosmeticDefectController,
} from '@pocitarna-nx-2023/database';
import { cosmeticDefectApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respond, respondWithPaging } from '../utils/respond';

export const cosmeticDefectRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(cosmeticDefectApi);

	router.post('/', scopeMiddleware('admin'), async (req, res) => {
		const cosmeticDefect = await new CosmeticDefectController().create(req.body);
		if (!cosmeticDefect) return res.status(500).json();

		respond<'createCosmeticDefect'>(true);
	});

	router.get('/', scopeMiddleware('admin', 'batchRead', 'batchWrite'), async () => {
		const listProps = getListProps();
		const [cosmeticDefects, getCount] = await new CosmeticDefectController().list({
			...listProps,
			sort: [...(listProps.sort || []), ['grade.sequence', 'asc']],
		});
		respondWithPaging<'getCosmeticDefects'>(cosmeticDefects, await getCount());
	});

	router.get('/:cosmeticDefectId', scopeMiddleware('home'), async (req, res) => {
		const cosmeticDefect = await new CosmeticDefectController().findById(req.params.cosmeticDefectId);
		if (!cosmeticDefect) return res.status(404).json();

		respond<'getCosmeticDefect'>(cosmeticDefect);
	});

	router.patch('/:cosmeticDefectId', scopeMiddleware('admin'), async (req) => {
		await new CosmeticDefectController().update(req.params.cosmeticDefectId, {
			...req.body,
			productCategories: req.body.productCategories ?? [],
			cosmeticAreas: req.body.cosmeticAreas ?? [],
		});

		respond<'updateCosmeticDefect'>(true);
	});

	router.post('/relation', scopeMiddleware('admin'), async (req) => {
		const { cosmeticDefectId, cosmeticAreaId, productId, customerClaimId, isFix } = req.body;

		if (productId) {
			await new ProductCosmeticDefectController().link({
				productId,
				cosmeticAreaId,
				cosmeticDefectIds: [cosmeticDefectId],
				isFix,
			});
		}

		if (customerClaimId) {
			await new CustomerClaimCosmeticDefectController().link({
				customerClaimId,
				cosmeticAreaId,
				cosmeticDefectIds: [cosmeticDefectId],
			});
		}

		respond<'establishCosmeticDefectRelation'>(true);
	});

	router.delete('/relation', scopeMiddleware('admin'), async (req) => {
		const { cosmeticDefectId, productId, customerClaimId } = req.body;

		if (productId) {
			await new ProductCosmeticDefectController().unlink(productId, cosmeticDefectId);
		}

		if (customerClaimId) {
			await new CustomerClaimCosmeticDefectController().unlink(customerClaimId, cosmeticDefectId);
		}

		respond<'removeCosmeticDefectRelation'>(true);
	});

	router.get('/relation/:relatedEntityId/files', scopeMiddleware('admin'), async (req) => {
		const { relatedEntityId } = req.params;
		const { relatedEntityType } = req.query;

		if (relatedEntityType === 'product') {
			const [files] = await new ProductCosmeticDefectController().listFiles(relatedEntityId);
			respond<'getCosmeticDefectRelationFiles'>(files);
		}

		if (relatedEntityType === 'customerClaim') {
			const [files] = await new CustomerClaimCosmeticDefectController().listFiles(relatedEntityId);
			respond<'getCosmeticDefectRelationFiles'>(files);
		}
	});

	router.patch('/relation/:relatedEntityId/files', scopeMiddleware('admin'), async (req) => {
		const { relatedEntityId } = req.params;
		const { fileId, sequence } = req.body;
		const { relatedEntityType } = req.query;

		if (relatedEntityType === 'product') {
			await new FileProductCosmeticDefectController().updateSequence(relatedEntityId, fileId, sequence);
		}

		if (relatedEntityType === 'customerClaim') {
			await new FileCustomerClaimCosmeticDefectController().updateSequence(relatedEntityId, fileId, sequence);
		}

		respond<'updateCosmeticDefectRelationFileSequence'>(true);
	});

	router.delete('/:cosmeticDefectId', scopeMiddleware('admin'), async (req, res) => {
		const cosmeticDefect = await new CosmeticDefectController().findById(req.params.cosmeticDefectId);
		if (!cosmeticDefect) return res.status(404).send();

		const result = await new CosmeticDefectController().delete(cosmeticDefect.id);

		respond<'deleteCosmeticDefect'>(result);
	});

	return router;
};
