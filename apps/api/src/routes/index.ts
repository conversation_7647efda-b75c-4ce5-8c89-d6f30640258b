import { api } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { errorBoundary } from '../middlewares/errorBoundary';
import { NextAuthMiddleware } from '../middlewares/nextAuth';
import { scopeMiddleware } from '../middlewares/scope';
import { respond } from '../utils/respond';
import { addressRouter } from './address';
import { analyticsRouter } from './analytics';
import { attributeRouter } from './attribute';
import { azureRouter } from './auth';
import { authAdminRouter } from './authAdmin';
import { batchRouter } from './batch';
import { conditionalAttributeRouter } from './conditionalAttribute';
import { contactRouter } from './contact';
import { cosmeticAreaRouter } from './cosmeticArea';
import { cosmeticDefectRouter } from './cosmeticDefect';
import { currencyRouter } from './currency';
import { customerClaimRouter } from './customerClaim';
import { customerClaimCosmeticDefectRouter } from './customerClaimCosmeticDefect';
import { defectTypeRouter } from './defectType';
import { ecommerceOrderRouter } from './ecommerceOrder';
import { filesRouter } from './files';
import { gradeRouter } from './grade';
import { batchHistoryRouter } from './history/batch';
import { customerClaimHistoryRouter } from './history/customerClaim';
import { productHistoryRouter } from './history/product';
import { productEnvelopeHistoryRouter } from './history/productEnvelope';
import { serviceCaseHistoryRouter } from './history/serviceCase';
import { serviceCenterHistoryRouter } from './history/serviceCenter';
import { shipmentHistoryRouter } from './history/shipment';
import { vendorHistoryRouter } from './history/vendor';
import { warehousePositionHistoryRouter } from './history/warehousePosition';
import { warrantyClaimHistoryRouter } from './history/warrantyClaim';
import { invalidateRouter } from './invalidate';
import { inventoryRouter } from './inventory';
import { noteRouter } from './note';
import { notificationRouter } from './notification';
import { notificationTypeRouter } from './notificationType';
import { outageRouter } from './outage';
import { printerRouter } from './printer';
import { productRouter } from './product';
import { productCategoryRouter } from './productCategories';
import { productCodeRouter } from './productCode';
import { productCosmeticDefectRouter } from './productCosmeticDefect';
import { productDefectRouter } from './productDefect';
import { productEnvelopeRouter } from './productEnvelope';
import { productTaskRouter } from './productTask';
import { publicRouter } from './public';
import { qrRouter } from './qr';
import { recyclingFeeRouter } from './recyclingFee';
import { savedFilterRouter } from './savedFilter';
import { scanRouter } from './scan';
import { searchRouter } from './search';
import { serviceCaseRouter } from './serviceCase';
import { serviceCenterRouter } from './serviceCenter';
import { serviceTaskRouter } from './serviceTask';
import { serviceTaskTypeRouter } from './serviceTaskType';
import { shipmentRouter } from './shipment';
import { shoptetAttributeRouter } from './shoptetAttribute';
import { shoptetCategoryRouter } from './shoptetCategory';
import { shoptetOrderSyncRouter } from './shoptetOrderSync';
import { userRouter } from './user';
import { userNotificationTypeRouter } from './userNotificationType';
import { vendorsRouter } from './vendors';
import { warehouseRouter } from './warehouse';
import { warehousePositionRouter } from './warehousePosition';
import { warehouseTaskRouter } from './warehouseTask';
import { warrantyClaimRouter } from './warrantyClaim';
import { webhookRouter } from './webhook';
import { wholesalePricingRouter } from './wholesalePricing';

export const rootRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(api);

	router.get('/', () => respond<'healthcheck'>(new Date()));
	router.get('/error', () => {
		throw new Error('Testing error');
	});

	router.use('/address', addressRouter(ctx));
	router.use('/analytics', analyticsRouter(ctx));
	router.use('/attribute', attributeRouter(ctx));
	router.use('/auth/admin', scopeMiddleware('admin'), authAdminRouter(ctx));
	router.use('/auth', NextAuthMiddleware, azureRouter(ctx));
	router.use('/batch', batchRouter(ctx));
	router.use('/contact', contactRouter(ctx));
	router.use('/conditional-attribute', conditionalAttributeRouter(ctx));
	router.use('/cosmetic-area', cosmeticAreaRouter(ctx));
	router.use('/cosmetic-defect', cosmeticDefectRouter(ctx));
	router.use('/currency', currencyRouter(ctx));
	router.use('/customer-claim/cosmetic-defect', customerClaimCosmeticDefectRouter(ctx));
	router.use('/customer-claim', customerClaimRouter(ctx));
	router.use('/defect-type', defectTypeRouter(ctx));
	router.use('/files', filesRouter(ctx));
	router.use('/grade', gradeRouter(ctx));
	router.use('/invalidate', invalidateRouter(ctx));
	router.use('/inventory', inventoryRouter(ctx));
	router.use('/note', noteRouter(ctx));
	router.use('/notification', notificationRouter(ctx));
	router.use('/notification-type', notificationTypeRouter(ctx));
	router.use('/order', ecommerceOrderRouter(ctx));
	router.use('/outage', outageRouter(ctx));
	router.use('/printer', printerRouter(ctx));
	router.use('/product/category', productCategoryRouter(ctx));
	router.use('/product/code', productCodeRouter(ctx));
	router.use('/product/cosmetic-defect', productCosmeticDefectRouter(ctx));
	router.use('/product/defect', productDefectRouter(ctx));
	router.use('/product/envelope', productEnvelopeRouter(ctx));
	router.use('/product/task', productTaskRouter(ctx));
	router.use('/product', productRouter(ctx));
	router.use('/public', publicRouter(ctx));
	router.use('/qr', qrRouter(ctx));
	router.use('/recycling-fee', recyclingFeeRouter(ctx));
	router.use('/saved-filter', savedFilterRouter(ctx));
	router.use('/scan', scanRouter(ctx));
	router.use('/search', searchRouter(ctx));
	router.use('/service-case', serviceCaseRouter(ctx));
	router.use('/service-center', serviceCenterRouter(ctx));
	router.use('/service-task', serviceTaskRouter(ctx));
	router.use('/service-task-type', serviceTaskTypeRouter(ctx));
	router.use('/shipment', shipmentRouter(ctx));
	router.use('/shoptet/attribute', shoptetAttributeRouter(ctx));
	router.use('/shoptet/category', shoptetCategoryRouter(ctx));
	router.use('/shoptet/order-sync', shoptetOrderSyncRouter(ctx));
	router.use('/user', userRouter(ctx));
	router.use('/user-notification-type', userNotificationTypeRouter(ctx));
	router.use('/vendor', vendorsRouter(ctx));
	router.use('/warehouse/position', warehousePositionRouter(ctx));
	router.use('/warehouse/task', warehouseTaskRouter(ctx));
	router.use('/warehouse', warehouseRouter(ctx));
	router.use('/warranty-claim', warrantyClaimRouter(ctx));
	router.use('/webhook', webhookRouter(ctx));
	router.use('/wholesale-pricing', wholesalePricingRouter(ctx));

	// History routers
	router.use('/history/batch', batchHistoryRouter(ctx));
	router.use('/history/customer-claim', customerClaimHistoryRouter(ctx));
	router.use('/history/product', productHistoryRouter(ctx));
	router.use('/history/product-envelope', productEnvelopeHistoryRouter(ctx));
	router.use('/history/service-case', serviceCaseHistoryRouter(ctx));
	router.use('/history/service-center', serviceCenterHistoryRouter(ctx));
	router.use('/history/shipment', shipmentHistoryRouter(ctx));
	router.use('/history/vendor', vendorHistoryRouter(ctx));
	router.use('/history/warehouse-position', warehousePositionHistoryRouter(ctx));
	router.use('/history/warranty-claim', warrantyClaimHistoryRouter(ctx));

	errorBoundary(router);
	return router;
};
