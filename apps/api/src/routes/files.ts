import { uploadFileToBucket } from '@pocitarna-nx-2023/aws';
import {
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	BatchDefectController,
	CustomerClaimController,
	FileBatchController,
	FileBatchDefectController,
	FileController,
	FileCustomerClaimController,
	FileInventoryController,
	FileProductCodeController,
	FileProductController,
	FileProductCosmeticDefectController,
	FileProductDefectController,
	FileServiceCaseController,
	FileVendorController,
	FileWarrantyClaimController,
	InventoryController,
	listAll,
	ProductCodeController,
	ProductController,
	ProductCosmeticDefectController,
	ProductDefectController,
	ServiceCaseController,
	VendorController,
	WarrantyClaimController,
} from '@pocitarna-nx-2023/database';
import { filterUndefined } from '@pocitarna-nx-2023/utils';
import { filesApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { handleUpload, handleUploadBase64 } from '../utils/handleUpload';
import { respond, respondWithPaging } from '../utils/respond';

export const filesRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(filesApi);

	router.get('/', scopeMiddleware('home'), async () => {
		const [files, getCount] = await new FileController().list(getListProps());
		respondWithPaging<'getFiles'>(files, await getCount());
	});

	router.get(
		'/:fileId/download',
		scopeMiddleware('batchRead', 'batchWrite', 'productRead', 'productWrite', 'testRead', 'productTest'),
		async (req, res) => {
			const downloadLink = await new FileController().getFileDownloadLink(req.params.fileId);
			res.status(307).location(downloadLink).json();
		},
	);

	router.get(
		'/:fileId/download/base64',
		scopeMiddleware('batchRead', 'batchWrite', 'productRead', 'productWrite', 'testRead', 'productTest'),
		async (req, res) => {
			const file = await new FileController().downloadFile(req.params.fileId);
			if (!file) return res.status(404).json();

			res.status(200).send(Buffer.from(file).toString('base64'));
		},
	);

	router.post('/upload', scopeMiddleware('batchWrite', 'productWrite', 'productTest'), async (req, res) => {
		const file = await handleUpload(req);
		const savedFile = await new FileController().saveFile(file, req.query.fileName);
		if (!savedFile) return res.status(500).json();

		respond<'uploadFile'>(savedFile.id);
	});

	router.post(
		'/upload/base64',
		scopeMiddleware(
			'batchWrite',
			'productWrite',
			'productTest',
			'serviceWrite',
			'warrantyClaimWrite',
			'productTestLead',
			'batchDelivery',
			'batchCheck',
		),
		async (req, res) => {
			const { id, type, name, photoSessionId } = req.body;
			const file = handleUploadBase64(req);
			const savedFile = await new FileController().saveFile(file, name, photoSessionId);
			if (!savedFile) return res.status(500).json();

			if (id && type) {
				await new FileController().handleFileAssociation({ id, type, savedFileId: savedFile.id });
			}

			respond<'uploadBase64'>(savedFile.id);
		},
	);

	router.get('/batch/:batchId', scopeMiddleware('batchRead', 'batchWrite', 'testRead', 'productTest'), async (req) => {
		const [files] = await new BatchController().listFiles(req.params.batchId);
		respond<'getBatchFiles'>(files);
	});

	router.patch('/batch/:batchId', scopeMiddleware('batchWrite'), async (req) => {
		const { batchId } = req.params;
		const { fileId, sequence } = req.body;
		await new FileBatchController().updateSequence(batchId, fileId, sequence);
		respond<'updateBatchFileSequence'>(true);
	});

	router.get('/product/:productId', scopeMiddleware('productRead', 'productWrite', 'testRead', 'productTest'), async (req) => {
		const [files] = await new ProductController().listFiles(req.params.productId, getListProps());
		respond<'getProductFiles'>(files);
	});

	router.patch('/product/:productId', scopeMiddleware('productWrite', 'productTestLead'), async (req) => {
		const { productId } = req.params;
		const { fileId, sequence } = req.body;
		await new FileProductController().updateSequence(productId, fileId, sequence);
		respond<'updateProductFileSequence'>(true);
	});

	router.get('/batch/defect/:defectId', scopeMiddleware('batchRead', 'batchWrite', 'testRead', 'productTest'), async (req) => {
		const [files] = await new BatchDefectController().listFiles(req.params.defectId);
		respond<'getBatchDefectFiles'>(files);
	});

	router.patch('/batch-defect/:batchDefectId', scopeMiddleware('batchWrite'), async (req) => {
		const { batchDefectId } = req.params;
		const { fileId, sequence } = req.body;
		await new FileBatchDefectController().updateSequence(batchDefectId, fileId, sequence);
		respond<'updateBatchDefectFileSequence'>(true);
	});

	router.get('/product/code/:productCodeId', scopeMiddleware('productRead', 'productWrite', 'testRead', 'productTest'), async (req) => {
		const [files] = await new ProductCodeController().listFiles(req.params.productCodeId);
		respond<'getProductCodeFiles'>(files);
	});

	router.patch('/product/code/:productCodeId', scopeMiddleware('productWrite', 'productTest', 'productTestLead'), async (req) => {
		const { productCodeId } = req.params;
		const { fileId, sequence } = req.body;
		await new FileProductCodeController().updateSequence(productCodeId, fileId, sequence);
		respond<'updateProductCodeFileSequence'>(true);
	});

	router.get(
		'/product/defect/:defectId',
		scopeMiddleware(
			'productRead',
			'productWrite',
			'testRead',
			'productTest',
			'serviceRead',
			'serviceWrite',
			'warrantyClaimRead',
			'warrantyClaimWrite',
		),
		async (req) => {
			const [files] = await new ProductDefectController().listFiles(req.params.defectId);
			respond<'getProductDefectFiles'>(files);
		},
	);

	router.patch('/product-defect/:productDefectId', scopeMiddleware('productWrite', 'productTest', 'productTestLead'), async (req) => {
		const { productDefectId } = req.params;
		const { fileId, sequence } = req.body;
		await new FileProductDefectController().updateSequence(productDefectId, fileId, sequence);
		respond<'updateProductDefectFileSequence'>(true);
	});

	router.get('/product/bulk/defect', scopeMiddleware('productRead', 'productWrite', 'testRead', 'productTest'), async (req) => {
		const result = await Promise.all(
			req.query.defectIds.map(async (defectId) => {
				const [files] = await new FileController().listProductDefectFiles(defectId, listAll());

				return filterUndefined(files);
			}),
		);

		respond<'getBulkProductDefectFiles'>(result.flat());
	});

	router.get(
		'/serviceCase/:serviceCaseId',
		scopeMiddleware(
			'serviceRead',
			'serviceWrite',
			'warrantyClaimRead',
			'warrantyClaimWrite',
			'productRead',
			'productWrite',
			'testRead',
			'productTest',
		),
		async (req) => {
			const [files] = await new ServiceCaseController().listFiles(req.params.serviceCaseId);
			respond<'getServiceCaseFiles'>(files);
		},
	);

	router.patch('/service-case/:serviceCaseId', scopeMiddleware('serviceWrite'), async (req) => {
		const { serviceCaseId } = req.params;
		const { fileId, sequence } = req.body;
		await new FileServiceCaseController().updateSequence(serviceCaseId, fileId, sequence);
		respond<'updateServiceCaseFileSequence'>(true);
	});

	router.get(
		'/warrantyClaim/:warrantyClaimId',
		scopeMiddleware('warrantyClaimRead', 'warrantyClaimWrite', 'productRead', 'productWrite'),
		async (req) => {
			const [files] = await new WarrantyClaimController().listFiles(req.params.warrantyClaimId);
			respond<'getWarrantyClaimFiles'>(files);
		},
	);

	router.patch('/warranty-claim/:warrantyClaimId', scopeMiddleware('warrantyClaimWrite'), async (req) => {
		const { warrantyClaimId } = req.params;
		const { fileId, sequence } = req.body;
		await new FileWarrantyClaimController().updateSequence(warrantyClaimId, fileId, sequence);
		respond<'updateWarrantyClaimFileSequence'>(true);
	});

	router.get('/customer-claim/:customerClaimId', scopeMiddleware('customerClaimRead', 'customerClaimWrite'), async (req) => {
		const [files] = await new CustomerClaimController().listFiles(req.params.customerClaimId, req.query.type);
		respond<'getCustomerClaimFiles'>(files);
	});

	router.patch('/customer-claim/:customerClaimId', scopeMiddleware('customerClaimWrite'), async (req) => {
		const { customerClaimId } = req.params;
		const { fileId, sequence } = req.body;
		await new FileCustomerClaimController().updateSequence(customerClaimId, fileId, sequence);
		respond<'updateCustomerClaimFileSequence'>(true);
	});

	router.get(
		'/vendor/:vendorId',
		scopeMiddleware('admin', 'warrantyClaimRead', 'warrantyClaimWrite', 'serviceRead', 'serviceWrite', 'batchRead', 'batchWrite'),
		async (req) => {
			const [files] = await new VendorController().listFiles(req.params.vendorId);
			respond<'getVendorFiles'>(files);
		},
	);

	router.patch('/vendor/:vendorId', scopeMiddleware('admin'), async (req) => {
		const { vendorId } = req.params;
		const { fileId, sequence } = req.body;
		await new FileVendorController().updateSequence(vendorId, fileId, sequence);
		respond<'updateVendorFileSequence'>(true);
	});

	router.get(
		'/inventory/:inventoryId',
		scopeMiddleware('admin', 'warrantyClaimRead', 'warrantyClaimWrite', 'serviceRead', 'serviceWrite', 'batchRead', 'batchWrite'),
		async (req) => {
			const [files] = await new InventoryController().listFiles(req.params.inventoryId);
			respond<'getInventoryFiles'>(files);
		},
	);

	router.patch('/inventory/:inventoryId', scopeMiddleware('admin'), async (req) => {
		const { inventoryId } = req.params;
		const { fileId, sequence } = req.body;
		await new FileInventoryController().updateSequence(inventoryId, fileId, sequence);
		respond<'updateInventoryFileSequence'>(true);
	});

	router.get(
		'/product/cosmetic-defect/:productCosmeticDefectId',
		scopeMiddleware('admin', 'warrantyClaimRead', 'warrantyClaimWrite', 'serviceRead', 'serviceWrite', 'batchRead', 'batchWrite'),
		async (req) => {
			const [files] = await new ProductCosmeticDefectController().listFiles(req.params.productCosmeticDefectId);
			respond<'getProductCosmeticDefectFiles'>(files);
		},
	);

	router.patch('/product/cosmetic-defect/:productCosmeticDefectId', scopeMiddleware('admin'), async (req) => {
		const { productCosmeticDefectId } = req.params;
		const { fileId, sequence } = req.body;
		await new FileProductCosmeticDefectController().updateSequence(productCosmeticDefectId, fileId, sequence);
		respond<'updateProductCosmeticDefectFileSequence'>(true);
	});

	router.get(
		'/photo-session/:photoSessionId',
		scopeMiddleware('batchRead', 'batchWrite', 'productRead', 'productWrite', 'testRead', 'productTest', 'batchDelivery'),
		async (req) => {
			const files = await new FileController().listFilesByPhotoSession(req.params.photoSessionId);
			respond<'getPhotoSessionFiles'>(files);
		},
	);

	router.get(
		'/uploaded-files',
		scopeMiddleware('batchRead', 'batchWrite', 'productRead', 'productWrite', 'testRead', 'productTest', 'batchDelivery'),
		async (req) => {
			const { photoSessionId, photoIds } = req.query;
			const files = await new FileController().listUploadedFiles(photoSessionId, photoIds);
			respond<'getUploadedFiles'>(files);
		},
	);

	router.post('/rotate', scopeMiddleware('batchWrite', 'productWrite'), async (req, res) => {
		const { fileId, rotation } = req.body;
		const file = await new FileController().findById(fileId);
		if (!file) return res.status(404).json();

		const fileBuffer = await new FileController().downloadFile(file.id);
		if (!fileBuffer) return res.status(404).json();

		const rotatedBuffer = await new FileController().rotate(fileBuffer, file.mime, rotation);
		const preview = await new FileController().generatePreview(rotatedBuffer, file.mime);

		await uploadFileToBucket(file.id, file.mime, rotatedBuffer, file.name);
		await new FileController().update(file, { preview });

		respond<'rotateImageFile'>(file);
	});

	router.delete('/', scopeMiddleware('batchWrite', 'productWrite'), async (req, res) => {
		const result = await new FileController().delete(req.body.ids);
		if (!result) return res.status(500).json();

		respond<'deleteFiles'>(result);
	});

	return router;
};
