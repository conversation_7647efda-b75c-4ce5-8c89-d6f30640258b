import { ProductCosmeticDefectController } from '@pocitarna-nx-2023/database';
import { productCosmeticDefectApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respondWithPaging } from '../utils/respond';

export const productCosmeticDefectRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(productCosmeticDefectApi);

	router.get('/', scopeMiddleware('productTest'), async () => {
		const [data, getCount] = await new ProductCosmeticDefectController().list(getListProps());
		respondWithPaging<'getProductCosmeticDefects'>(data, await getCount());
	});

	return router;
};
