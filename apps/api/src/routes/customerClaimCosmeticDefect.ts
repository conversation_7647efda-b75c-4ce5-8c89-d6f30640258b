import { CustomerClaimCosmeticDefectController } from '@pocitarna-nx-2023/database';
import { customerClaimCosmeticDefectApi } from '@pocitarna-nx-2023/zodios';
import type { zodiosContext } from '@zodios/express';
import { scopeMiddleware } from '../middlewares/scope';
import { getListProps } from '../utils/getListProps';
import { respondWithPaging } from '../utils/respond';

export const customerClaimCosmeticDefectRouter = (ctx: ReturnType<typeof zodiosContext>) => {
	const router = ctx.router(customerClaimCosmeticDefectApi);

	router.get('/', scopeMiddleware('customerClaimRead', 'customerClaimWrite'), async () => {
		const [data, getCount] = await new CustomerClaimCosmeticDefectController().list(getListProps());
		respondWithPaging<'getCustomerClaimCosmeticDefects'>(data, await getCount());
	});

	return router;
};
