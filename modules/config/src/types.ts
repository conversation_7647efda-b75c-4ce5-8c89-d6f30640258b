import {
	AFTER_SERVICE_PRODUCT_TARGET_STATUSES,
	type AFTER_WARRANTY_CLAIM_PRODUCT_TARGET_STATUSES,
	type AUTH_TYPES,
	type BASE_INVENTORY_ITEM_STATUS_NAMES,
	type BATCH_DELIVERY_TYPE_NAMES,
	type BATCH_FILE_TYPE,
	type BATCH_STATUS_NAMES,
	type BATCH_STATUS_TRANSITION_TIMESTAMPS,
	type BATCH_TYPES,
	type COMMON_STATUSES,
	type CUSTOMER_CLAIM_DELIVERY_METHODS,
	type CUSTOMER_CLAIM_FILE_TYPE,
	type CUSTOMER_CLAIM_HANDLING_METHODS,
	type C<PERSON><PERSON>MER_CLAIM_STATUS_NAMES,
	type ENVE<PERSON>OPE_TYPES,
	type HTT<PERSON>_METHODS_NAMES,
	type INVENTORY_ITEM_STATUS_NAMES,
	type INVE<PERSON><PERSON>Y_STATUS_NAMES,
	type NOTIFICATION_DELIVERY_METHODS_NAMES,
	type NOTIFICATION_TYPE_NAMES,
	type ORDER_ITEM_TYPES,
	type PAYMENT_STATUS_NAMES,
	type PRICE_ROUNDING_STRATEGIES,
	type PRINTER_LOCATIONS,
	type PRIORITY_NAMES,
	type PRODUCT_ATTRIBUTE_DATATYPE,
	type PRODUCT_ATTRIBUTE_VALUE_TYPE,
	type PRODUCT_CATEGORY_ATTRIBUTE_TYPE,
	type PRODUCT_DEFECT_SOURCE,
	type PRODUCT_FILE_TYPE,
	type PRODUCT_PRICE_TYPES,
	type PRODUCT_STATUS_NAMES,
	type PRODUCT_TASK_STATUS_TYPES,
	type PRODUCT_TYPES,
	type SCOPE_NAMES,
	type SERVICE_CASE_STATUS_NAMES,
	type SERVICE_CASE_TYPE_NAMES,
	type SERVICE_FLOWS,
	type SERVICE_TASK_RESOLUTION_SCOPE,
	type SERVICE_TASK_STATUS_TYPES,
	type SERVICE_TASK_TYPE_NAMES,
	type SHIPMENT_RELATED_STATUSES_NAMES,
	type SHIPMENT_STATUS_NAMES,
	type SHOPTET_COUNTRIES,
	type SUPPORTED_CURRENCIES,
	type UNIQUE_PRODUCT_PRICE_TYPES,
	type WAREHOUSE_BOXES,
	type WAREHOUSE_RACKS,
	type WAREHOUSE_SHELVES,
	type WAREHOUSE_TASK_STATUSES,
	type WAREHOUSE_TASK_TYPES,
	type WAREHOUSE_TYPE_NAMES,
	type WARRANTY_CLAIM_STATUS_NAMES,
	type WARRANTY_TYPES,
} from './constants';

export type Stage = 'dev' | 'staging' | 'prod';

export type CurrencyCode = (typeof SUPPORTED_CURRENCIES)[number];

export type AuthenticationType = (typeof AUTH_TYPES)[number];

export type AttributeDatatype = (typeof PRODUCT_ATTRIBUTE_DATATYPE)[number];
export type AttributeCategoryType = (typeof PRODUCT_CATEGORY_ATTRIBUTE_TYPE)[number];
export type AttributeValueType = (typeof PRODUCT_ATTRIBUTE_VALUE_TYPE)[number];
export type ProductFileType = (typeof PRODUCT_FILE_TYPE)[number];
export type BatchFileType = (typeof BATCH_FILE_TYPE)[number];

export type CommonStatus = (typeof COMMON_STATUSES)[number];

export type ShipmentRelatedStatus = (typeof SHIPMENT_RELATED_STATUSES_NAMES)[number];
export type BatchStatus = (typeof BATCH_STATUS_NAMES)[number];
export type ProductStatus = (typeof PRODUCT_STATUS_NAMES)[number];
export type ServiceTaskType = (typeof SERVICE_TASK_TYPE_NAMES)[number];
export type ProductTaskStatus = (typeof PRODUCT_TASK_STATUS_TYPES)[number];
export type ServiceTaskStatus = (typeof SERVICE_TASK_STATUS_TYPES)[number];
export type ServiceCaseStatus = (typeof SERVICE_CASE_STATUS_NAMES)[number];
export type ServiceCaseType = (typeof SERVICE_CASE_TYPE_NAMES)[number];
export type WarrantyClaimStatus = (typeof WARRANTY_CLAIM_STATUS_NAMES)[number];

export type CustomerClaimStatus = (typeof CUSTOMER_CLAIM_STATUS_NAMES)[number];
export type CustomerClaimHandlingMethod = (typeof CUSTOMER_CLAIM_HANDLING_METHODS)[number];
export type CustomerClaimDeliveryMethod = (typeof CUSTOMER_CLAIM_DELIVERY_METHODS)[number];
export type CustomerClaimFileType = (typeof CUSTOMER_CLAIM_FILE_TYPE)[number];

export type PaymentStatus = (typeof PAYMENT_STATUS_NAMES)[number];

export type PriorityLevel = (typeof PRIORITY_NAMES)[number];

export type ScopeName = (typeof SCOPE_NAMES)[number];

export type HttpMethod = (typeof HTTP_METHODS_NAMES)[number];

export type BatchStatusTransitionTimestamp = (typeof BATCH_STATUS_TRANSITION_TIMESTAMPS)[number];

export type BatchDeliveryType = (typeof BATCH_DELIVERY_TYPE_NAMES)[number];

export type ServiceFlow = (typeof SERVICE_FLOWS)[number];

export type ShipmentStatus = (typeof SHIPMENT_STATUS_NAMES)[number];

export type UniqueProductPriceType = (typeof UNIQUE_PRODUCT_PRICE_TYPES)[number];

export type ProductPriceType = (typeof PRODUCT_PRICE_TYPES)[number];

export type WarrantyType = (typeof WARRANTY_TYPES)[number];

export type PrinterLocation = (typeof PRINTER_LOCATIONS)[number];

export type WarehouseType = (typeof WAREHOUSE_TYPE_NAMES)[number];
export type WarehouseRack = (typeof WAREHOUSE_RACKS)[number];
export type WarehouseShelf = (typeof WAREHOUSE_SHELVES)[number];
export type WarehouseBox = (typeof WAREHOUSE_BOXES)[number];
export type WarehouseTaskStatus = (typeof WAREHOUSE_TASK_STATUSES)[number];
export type WarehouseTaskType = (typeof WAREHOUSE_TASK_TYPES)[number];

export type OrderItemType = (typeof ORDER_ITEM_TYPES)[number];

export type BatchType = (typeof BATCH_TYPES)[number];
export type EnvelopeType = (typeof ENVELOPE_TYPES)[number];
export type ProductType = (typeof PRODUCT_TYPES)[number];

export type EmailSpecification = {
	recipients: string | string[];
	cc?: string | string[];
	subject: string;
	message: string;
	attachments?: { filename: string; path: string }[];
};

export type ShoptetCountry = (typeof SHOPTET_COUNTRIES)[number];

export type ProductDefectSource = (typeof PRODUCT_DEFECT_SOURCE)[number];

export type AfterServiceProductTargetStatus = (typeof AFTER_SERVICE_PRODUCT_TARGET_STATUSES)[number];
export const isAfterServiceProductTargetStatus = (status: unknown): status is AfterServiceProductTargetStatus => {
	return AFTER_SERVICE_PRODUCT_TARGET_STATUSES.includes(status as AfterServiceProductTargetStatus);
};

export type AfterWarrantyClaimProductTargetStatus = (typeof AFTER_WARRANTY_CLAIM_PRODUCT_TARGET_STATUSES)[number];

export type ServiceTaskResolutionScope = (typeof SERVICE_TASK_RESOLUTION_SCOPE)[number];

export type InventoryStatus = (typeof INVENTORY_STATUS_NAMES)[number];
export type BaseInventoryItemStatus = (typeof BASE_INVENTORY_ITEM_STATUS_NAMES)[number];
export type InventoryItemStatus = (typeof INVENTORY_ITEM_STATUS_NAMES)[number];

export type PriceRoundingStrategy = (typeof PRICE_ROUNDING_STRATEGIES)[number];

export type NotificationType = (typeof NOTIFICATION_TYPE_NAMES)[number];
export type NotificationDeliveryMethod = (typeof NOTIFICATION_DELIVERY_METHODS_NAMES)[number];
