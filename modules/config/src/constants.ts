import { endOfWeek, startOfWeek } from 'date-fns';
import type { CurrencyCode, PriorityLevel, Stage } from './types';

export const STAGE: Stage = (process.env['STAGE'] ?? process.env['NEXT_PUBLIC_STAGE'] ?? 'dev') as Stage;
export const IS_STAGING = STAGE === 'staging';
export const IS_PROD = STAGE === 'prod';
export const IS_DEV = STAGE === 'dev';
export const IS_CI = process.env['CI'] ?? false;

export const ONE_SECOND = 1000;
export const TWO_SECONDS = 2 * ONE_SECOND;
export const FIVE_SECONDS = 5 * ONE_SECOND;
export const TEN_SECONDS = 10 * ONE_SECOND;
export const THIRTY_SECONDS = 30 * ONE_SECOND;
export const ONE_MINUTE = 60 * ONE_SECOND;
export const TWO_MINUTES = 2 * ONE_MINUTE;
export const FIVE_MINUTES = 5 * ONE_MINUTE;
export const TEN_MINUTES = 10 * ONE_MINUTE;
export const ONE_HOUR = 60 * ONE_MINUTE;
export const EIGHT_HOURS = 8 * ONE_HOUR;
export const ONE_DAY = 24 * ONE_HOUR;
export const TWO_DAYS = 2 * ONE_DAY;
export const ONE_WEEK = 7 * ONE_DAY;
export const TWO_WEEKS = 2 * ONE_WEEK;
export const ONE_MONTH = 30 * ONE_DAY;
export const ONE_YEAR = 365 * ONE_DAY;
export const TWO_YEARS = 2 * ONE_YEAR;
export const TWENTY_YEARS = 20 * ONE_YEAR;

export const TIMEZONE = 'Europe/Prague';

export const ONE_KIBIBYTE = 2 ** 10;
export const ONE_MEBIBYTE = 2 ** 20;
export const ONE_GIBIBYTE = 2 ** 30;
export const ONE_TEBIBYTE = 2 ** 40;

export const INCH_TO_CM = 2.54;
export const CM_TO_INCH = 1 / INCH_TO_CM;

export const MAX_NEGATIVE_INTEGER = -(2 ** (32 - 1));
export const MAX_POSITIVE_INTEGER = 2 ** (32 - 1) - 1;

export const URL_PATTERN = /[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)/;

export const PAGINATION_LIMIT = 10;
export const CODE_LENGTH = 8;
export const CODE_PREFIX = {
	PRODUCT: 'PCN',
	BATCH: 'V',
	BATCH_DELIVERY: 'P',
	BATCH_CHECK: 'K',
	BATCH_TEST: 'T',
	SERVICE_CASE: 'S',
	SHIPMENT: 'E',
	WARRANTY_CLAIM: 'DR',
	CUSTOMER_CLAIM: 'ZR',
	INVENTORY: 'INV',
	WAREHOUSE_POSITION: 'WHP',
};

export const LOCALE = 'cs-CZ';

export const APP_NAME = 'pocitarna-nx-2023';
export const DOMAIN_NAME = 'pocitarna.com';
export const APP_URL = process.env['NEXTAUTH_URL'];
export const SHOPTET_URL: Record<Stage, string> = {
	dev: 'https://697094.myshoptet.com',
	prod: 'https://www.pocitarna.cz',
	staging: 'https://697094.myshoptet.com',
};
export const SHOPTET_ADMIN_URL = `${SHOPTET_URL[STAGE]}/admin`;

export const CA_CRT = `-----BEGIN CERTIFICATE-----
MIIEEzCCAvugAwIBAgIUSZwYJ5ydwMHMexrMy39ymapaN0QwDQYJKoZIhvcNAQEL
BQAwgZgxCzAJBgNVBAYTAkNaMRowGAYDVQQIDBFKaWhvbW9yYXZza3kga3JhajEN
MAsGA1UEBwwEQnJubzEXMBUGA1UECgwOTWFydGluIEJyeWNodGExFjAUBgNVBAMM
DXBvY2l0YXJuYS5jb20xLTArBgkqhkiG9w0BCQEWHm1hcnRpbi5icnljaHRhQHN1
cGVya29kZXJzLmNvbTAeFw0yNDA5MTIwOTI3NDJaFw00NDA5MTIwOTI3NDJaMIGY
MQswCQYDVQQGEwJDWjEaMBgGA1UECAwRSmlob21vcmF2c2t5IGtyYWoxDTALBgNV
BAcMBEJybm8xFzAVBgNVBAoMDk1hcnRpbiBCcnljaHRhMRYwFAYDVQQDDA1wb2Np
dGFybmEuY29tMS0wKwYJKoZIhvcNAQkBFh5tYXJ0aW4uYnJ5Y2h0YUBzdXBlcmtv
ZGVycy5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDBsmnL7cVo
2Vw7jQnCdbhzEA/iuTO98UkMYKvUoozS/EO/dp1gsaecXXN3yTH+KCqx7p/gOyHA
iRRI2Oj2RBItJtIH2tk03pE0frX1wO054OXgLLLKznpzSKXGgU3fQyaGSE4YPGm5
klKRVBMTZLP00hK1dnuhFgtOVSA3+VFOBxULCXXZrt4Ef1AR8teumOV8YAypuEVD
tx9X8SUr+H4CorHiGRN9oyiYejpHhA7T2VywirXtZ2Kzjix5t+JtHgZ+4eezuGZm
IUanuXltj82dUHQYHCOlVvGGgtFW7V6wG/j4xeu36ud6+stgwIupQ3KRe8305BBM
cR2SsCevrHU7AgMBAAGjUzBRMB0GA1UdDgQWBBT9b6mZbv+1cFdB6LvDlPtja7cU
tzAfBgNVHSMEGDAWgBT9b6mZbv+1cFdB6LvDlPtja7cUtzAPBgNVHRMBAf8EBTAD
AQH/MA0GCSqGSIb3DQEBCwUAA4IBAQCWC368oOI7ldO69yqvCUdcTHOR6v11XTvG
U6MBCx4gwPsh53zAJLF52JExpG6kkIK00KyFRqQH2JJTUCUEwmH6N2pDAD9QoyJ2
CfqFX78WqBZB5I/2hmPuc6ufVorCZkvKKQILvOlynShTf8ZY3jC1c/TjAIHyDOAD
r4W6/qwpEo8IzNG2WdzcNOgwQDTAd5TqSgluei9Dhvt6FQ4+2XlRYm0qpRz+VU4r
LK312EB9vpOnAdlw4NTGwIiCUBlxLYKHtg9d4hsDt1Oeu1fdTQDMRbxDJCVF421j
Zorbrqbcr30Fs4t+GCKbwmQQIjmScl5PxBRlRzLsJgZvz1AQa3jb
-----END CERTIFICATE-----
`;

export const CA_KEY = `-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIFJDBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQtIYteFeiGK6OueUA
PZV+JAICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQIJH42TdxS/nUEggTI
3tR4HNeDSr9KCwHLv7UFR4nSkOlk2UvvXfPOxiQokT5nkIifvZFL6qtTkYuxslZR
nyZgKaI/MYgDRhD8+tLsqkg2UMsKNp3JbdPDvfXniQ1n7jO84miCMbHzFHq3x7Oz
RqbJN9k+7Kqore6ztvBd/YX1DTxftVZ336+8VUf6NBTpYDDsxZq76zxoCsxft/4h
AyZyOJWsAqzkvouANVBY//bQdDOIriUi6PoAMhlkUqBgbKpPsol9pezbSycJ+bf/
KZc9O5l3catvjO90sJVQjtZkl9WpScT2VS1uBwSiZ59/V6ZJnnBVLpWtLk4eerJC
hay6f0+AZ0jxUo1/37M0IjaPFMqECyWzqENApflGHJ8thuYTalaFjIQhLbkus7k5
atuokxk+Jd8hl527Sxc9Mnt3N0Xp0X2jmjqtNx/c6XKf/R07nROqZNoSNPu6Euw8
Djm4eL1Ea+aUIm56N/6MK51TuiD9h0BwRF+HtKxIUBWKblyF6oVimVd50LGrIprS
AFeGu81QfxOzDahb7OWG+PZiwOxBhVJ06lrHSN3436BJYNcxsnDm39BvQe9GDkVB
lLGrHPAOJ24QbY+dM7ulR9QAhG+eWwyzdkIUty+uDSeRaSpQjpfsOXYlmCvLHJjp
42+ztBpi7zKfCyUltFf8r+/9lmeBRTtKCrL0KWR8IF0so4qMOMZ+V2E2K2TA4zLK
G/Uk4921moR5aqVm8StUSuRtl23kKe40v/LaVOAybiF1FoWBTPgz5jgL3PboHuqb
is7IN++NlxMdLClQQmSMDFZy7rUCznOVg0uuFcLaTfsx5/nI8xpOy0+CvOwx8SBf
ZM4WgFfK5SH3Fhypy8tUF7kvtu8F4hnnZEhLO7fJ212W9pTRakfXkJlgYYScXONC
3MP6YnxPQsUR0TZtOArlQjmpy7rFs1rdDCFqQ3kta6QpftT0b0PuOgu3R4mdz7TR
y72aREbsoBvcyn5CpWrUp/bfKm7+Z7E4MusIyl6BZQL0VzIYjY+BPWCGtXv3b8ia
qPPifwt+/V0Hk8/UHQC7WdrNHdauTR6vOwjEfp8Jnb6D7xu7ljvZCwYn2Sq2X7m+
0m2FoK4N7t28JYy/BM8lgO8TBa37uYnzoPohEw4nF++o8cXlbcci4S/WhFBZ4mvG
xHfzuQgwY9/spBci2X2HWeMttVDnuHYrkcp5WrdHpu37jTgRIEo4LvZAnC9o4MCm
jZ0oaJzIAA9lFZXcg5ATG6Lq4ArhSKyQWYmJeGqMxxuRdv1MwuQ3jRd7lMG9DyiI
e82QLPqKNCGRTXsAQtVqXpdt4SLsSbqMQRnyNZC/1WXlVoJUAdOSTzZF52wufnW3
7RDuhndtr/J5AkzPUN3ps0PxG/96dtzzcmZzXl3aMjDlsY/Jveh7RRqM0E2zmUvT
oKO1C89NUN6hUV8FBRGEzwVmvY3a2boNnN1JAYQ5gVaYmG3jWNUKWgQMTPFbxNCz
NpQNpl2UxluaQP18qxLAPyQxvAASHiBWl7xI7ktljwJC2D2ZSsJd0nTqAQ6K2nzL
LID0lwYHw1N1s0qCzUBsNBXotMxVWqrGpvfdMXL6SDY4NXvj2Q8TSd629m92gSMV
JIOoBhXwLiGqLK41awobEKquQhqB44Ur
-----END ENCRYPTED PRIVATE KEY-----
`;

export const SUPERKODERS_IPV4_CIDR = '************/32';
export const MAB_IPV4_CIDR = '************/32';
export const POCITARNA_IPV4_CIDR = '*************/32';
export const GITLAB_URL = 'https://gitlab.superkoders.com';
export const GITLAB_REGISTRY_USER = 'ecs';
export const GITLAB_REGISTRY_PASS = 'gldt-********************';

export const AWS_ACCOUNT: Record<Stage, string> = {
	dev: '************',
	prod: '************',
	staging: '************',
};

export const AWS_REGION = 'eu-central-1';

export const DB_HOST = process.env['DB_HOST'] ?? 'localhost';
export const DB_PORT = parseInt(process.env['DB_PORT'] ?? '5432');
export const DB_USER = process.env['DB_USER'] ?? 'postgres';
export const DB_PASS = process.env['DB_PASS'] ?? 'rootroot';
export const DB_NAME = APP_NAME.replace(/-/g, '');

export const LOCAL_S3_HOST = process.env['S3_HOST'] ?? 'localhost';
export const LOCAL_S3_PORT = process.env['S3_PORT'] ?? '9000';
export const LOCAL_S3_USER = process.env['S3_USER'] ?? 'root';
export const LOCAL_S3_PASS = process.env['S3_PASS'] ?? 'rootroot';

export const LOCAL_DDB_HOST = process.env['DDB_HOST'] ?? 'localhost';
export const LOCAL_DDB_PORT = process.env['DDB_PORT'] ?? '9002';
export const LOCAL_DDB_USER = process.env['DDB_USER'] ?? 'root';
export const LOCAL_DDB_PASS = process.env['DDB_PASS'] ?? 'rootroot';

export const LOCAL_LAMBDA_HOST = process.env['LAMBDA_HOST'] ?? 'localhost';
export const LOCAL_LAMBDA_PORT = process.env['LAMBDA_PORT'] ?? '3001';
export const LOCAL_LAMBDA_USER = process.env['LAMBDA_USER'] ?? 'root';
export const LOCAL_LAMBDA_PASS = process.env['LAMBDA_PASS'] ?? 'rootroot';

export const LOCAL_SES_HOST = process.env['SES_HOST'] ?? 'localhost';
export const LOCAL_SES_PORT = process.env['SES_PORT'] ?? '9003';
export const LOCAL_SES_USER = process.env['SES_USER'] ?? 'root';
export const LOCAL_SES_PASS = process.env['SES_PASS'] ?? 'rootroot';

export const LOCAL_SQS_HOST = process.env['SQS_HOST'] ?? 'localhost';
export const LOCAL_SQS_PORT = process.env['SQS_PORT'] ?? '9324';
export const LOCAL_SQS_USER = process.env['SQS_USER'] ?? 'root';
export const LOCAL_SQS_PASS = process.env['SQS_PASS'] ?? 'rootroot';
export const SHOPTET_SQS_URL =
	process.env['SHOPTET_SQS_URL'] ?? `http://${LOCAL_SQS_HOST}:${LOCAL_SQS_PORT}/${AWS_ACCOUNT[STAGE]}/Resources-ShoptetExportsQueue`;
export const PRODUCT_PRICES_SYNC_SQS_URL =
	process.env['PRODUCT_PRICES_SYNC_SQS_URL'] ??
	`http://${LOCAL_SQS_HOST}:${LOCAL_SQS_PORT}/${AWS_ACCOUNT[STAGE]}/Resources-ProductPricesSyncQueue`;
export const ORDER_SYNC_SQS_URL =
	process.env['ORDER_SYNC_SQS_URL'] ?? `http://${LOCAL_SQS_HOST}:${LOCAL_SQS_PORT}/${AWS_ACCOUNT[STAGE]}/Resources-OrderSyncQueue`;
export const ORDER_UPDATE_SQS_URL =
	process.env['ORDER_UPDATE_SQS_URL'] ?? `http://${LOCAL_SQS_HOST}:${LOCAL_SQS_PORT}/${AWS_ACCOUNT[STAGE]}/Resources-OrderUpdateQueue`;
export const ORDER_ITEM_STATUS_UPDATE_SQS_URL =
	process.env['ORDER_ITEM_STATUS_UPDATE_SQS_URL'] ??
	`http://${LOCAL_SQS_HOST}:${LOCAL_SQS_PORT}/${AWS_ACCOUNT[STAGE]}/Resources-OrderItemStatusUpdateQueue`;
export const STOCK_DRAIN_SQS_URL =
	process.env['STOCK_DRAIN_SQS_URL'] ??
	`http://${LOCAL_SQS_HOST}:${LOCAL_SQS_PORT}/${AWS_ACCOUNT[STAGE]}/Resources-ShoptetStockDrainQueue`;

export const API_ORIGIN = process.env['NEXT_PUBLIC_API_ORIGIN'] ?? 'http://localhost:3333';
export const SSE_ENDPOINT = '/sse';
export const SSE_MESSAGE = {
	NOTIFICATION: 'notification',
	INVALIDATION: 'invalidation',
} as const;

export const ALLOWED_ORIGINS = [APP_URL, SHOPTET_URL[STAGE]];

export const AZURE_AD_TENANT_ID = process.env['AZURE_AD_TENANT_ID'] ?? 'fa9bafb2-763d-4511-8e1a-de1b80b1ff49';
export const AZURE_AD_CLIENT_ID = process.env['AZURE_AD_CLIENT_ID'] ?? 'c5b4c4c8-cc99-46dc-8f8c-4cfb7bf57d36';
export const AZURE_AD_CLIENT_SECRET = process.env['AZURE_AD_CLIENT_SECRET'] ?? '****************************************';

export const DEFAULT_ETA = TWO_WEEKS;
export const DEFAULT_WARRANTY = TWO_YEARS;

export const SUPPORTED_CURRENCIES = ['EUR', 'USD', 'GBP', 'CZK'] as const;
export const CURRENCIES_SYMBOLS: Record<CurrencyCode, string> = {
	EUR: '€',
	GBP: '£',
	USD: '$',
	CZK: 'Kč',
};

export const AUTH_TYPES = ['azure-ad', 'password', 'verificationCode'] as const;
export const ADMIN_ROLE_NAME = 'Admin';
export const DEFAULT_ROLE_NAME = 'Home';
export const SCOPE_NAMES = [
	'admin',
	'home',
	'batchRead',
	'batchWrite',
	'batchCheck',
	'batchDelivery',
	'customerClaimRead',
	'customerClaimWrite',
	'envelopeWrite',
	'productTest',
	'productTestLead',
	'testRead',
	'serviceRead',
	'serviceWrite',
	'serviceManage',
	'warrantyClaimRead',
	'warrantyClaimWrite',
	'stock',
	'productRead',
	'productWrite',
	'orderRead',
	'orderWrite',
	'productAdmin',
	'shoptetWrite',
	'warehouseManage',
	'warehouseRead',
	'warehouseWrite',
] as const;

export const COMMON_STATUSES = ['AT_SUPPLIER', 'ON_THE_WAY', 'EXTERNAL_STOCK', 'TO_CHECK', 'TO_CHECK_SN', 'TO_TEST'] as const;
export const BATCH_STATUS_NAMES = ['IMPORTING', ...COMMON_STATUSES, 'MARKETING', 'CLOSED'] as const;
export const SHIPMENT_RELATED_STATUSES_NAMES = ['WAITING_FOR_SHIPMENT', 'SHIPMENT_DISPATCHED', 'SHIPMENT_DELIVERED'] as const;
export const PRODUCT_STATUS_NAMES = [
	...COMMON_STATUSES,
	'TESTED',
	'MISSING',
	'SERVICE',
	'WARRANTY_CLAIM',
	'STOCK',
	'FOR_SALE',
	'RESERVED',
	'SOLD',
	'CUSTOMER_CLAIM',
	'DEAD',
	'AUTOPSY',
	'RETURNED',
	'LOST_FROM_INVENTORY',
	...SHIPMENT_RELATED_STATUSES_NAMES,
] as const;
export const AFTER_SERVICE_PRODUCT_TARGET_STATUSES = ['DEAD', 'AUTOPSY', 'TO_TEST', 'STOCK', 'SOLD'] as const;
export const AFTER_WARRANTY_CLAIM_PRODUCT_TARGET_STATUSES = ['AUTOPSY', 'TO_TEST', 'STOCK'] as const;
export const PAYMENT_STATUS_NAMES = ['INITIATED', 'PAID', 'UNPAID'] as const;
export const SERVICE_TASK_TYPE_NAMES = ['TESTING', 'SERVICE'] as const;
export const PRODUCT_TASK_STATUS_TYPES = ['NEW', 'CLOSED'] as const;
export const SERVICE_TASK_STATUS_TYPES = ['NEW', 'CLOSED'] as const;
export const SERVICE_CASE_STATUS_NAMES = [
	'NEW',
	'ASSIGNED_TO_INTERNAL_SERVICE',
	'ASSIGNED_TO_EXTERNAL_SERVICE',
	'SENT_TO_SERVICE_CENTER',
	'OFFER_RECEIVED',
	'WAITING_FOR_REPAIR',
	'OFFER_REJECTED',
	'WAITING_FOR_RETURN',
	'WAITING_FOR_PRODUCT',
	'WAITING_FOR_BUYBACK',
	'CLOSED',
] as const;
export const SERVICE_CASE_TYPE_NAMES = ['FRONTEND', 'BACKOFFICE'] as const;
export const WARRANTY_CLAIM_STATUS_NAMES = [
	'NEW',
	'WAITING_FOR_VENDOR',
	'SENT_TO_VENDOR',
	'WAITING_FOR_RETURN',
	'CLOSED',
	'REJECTED',
	'REFUNDED',
	'DISCOUNT',
	'RESOLVED_BY_VENDOR',
	'TRADED_PIECE',
	'WAITING_FOR_DISCOUNT',
	'WAITING_FOR_REPAIR',
] as const;
export const CUSTOMER_CLAIM_STATUS_NAMES = [
	'NEW',
	'RECEIVED',
	'PROCESSING',
	'WAITING_FOR_SPARE_PART',
	'SENT_TO_EXTERNAL_SERVICE',
	'WAITING_FOR_RESOLUTION',
	'RESOLVED',
	'CANCELLED',
	'SENT_BACK',
] as const;
export const CUSTOMER_CLAIM_HANDLING_METHODS = ['EXCHANGE', 'REPAIR', 'REFUND'] as const;
export const CUSTOMER_CLAIM_DELIVERY_METHODS = ['IN_PERSON', 'SHIPMENT'] as const;
export const CUSTOMER_CLAIM_MAX_PROCESSING_DAYS = 30;
export const CUSTOMER_CLAIM_FILE_TYPE = ['creation', 'receipt', 'final', 'regular'] as const;
export const CUSTOMER_CLAIM_WARNING_DAYS = 7;
export const CUSTOMER_CLAIM_CANCELLATION_DAYS = 14;

export const ENTITY_MANAGER_LABEL = 'ENTITY_MANAGER';

export const DEFAULT_ERROR_MESSAGE = 'Něco se pokazilo...';

export const PRODUCT_ATTRIBUTE_DATATYPE = ['boolean', 'number', 'string', 'text', 'multiselect'] as const;
export const PRODUCT_CATEGORY_ATTRIBUTE_TYPE = [
	'default',
	'testing',
	'compare',
	'mandatory',
	'envelope',
	'name',
	'export',
	'list',
	'config',
	'stock',
	'report',
] as const;
export const PRODUCT_ATTRIBUTE_VALUE_TYPE = ['import', 'reader', 'resolved', 'service'] as const;
export const PRODUCT_FILE_TYPE = ['import', 'reader', 'regular'] as const;
export const BATCH_FILE_TYPE = ['import', 'delivery', 'regular'] as const;

export const NOT_AVAILABLE = 'N/A';
export const LOADING_MESSAGE = 'Načítám...';

export const PRIORITY_NAMES = ['LOW', 'MEDIUM', 'HIGH'] as const;
export const PRIORITY_VALUES: Record<PriorityLevel, number> = {
	LOW: 0,
	MEDIUM: 50,
	HIGH: 100,
};
export const BATCH_DELIVERY_TYPE_NAMES = ['VENDOR', 'OWN'] as const;

export const HTTP_METHODS_NAMES = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'] as const;

export const READER_PORT = 60000;
export const READER_ORIGIN = `http://localhost:${READER_PORT}`;
export const READER_LINKS = [
	`${READER_ORIGIN}/hwinfo`,
	`${READER_ORIGIN}/systeminfo`,
	`${READER_ORIGIN}/gpuz`,
	`${READER_ORIGIN}/pnp`,
	`${READER_ORIGIN}/aggreg`,
	`${READER_ORIGIN}/ios`,
	`${READER_ORIGIN}/android`,
] as const;
export const READER_DOWNLOAD_LINKS = [
	{
		label: 'Linux arm64',
		url: 'https://drive.google.com/file/d/1lNtjiqfyx9t4Lfu5a_owNQupMpF06WVP/view?usp=drive_link',
	},
	{
		label: 'Linux x64',
		url: 'https://drive.google.com/file/d/12HZb25EtbSPZKjW0L_OboHJecRTem_xY/view?usp=drive_link',
	},
	{
		label: 'MacOS arm64',
		url: 'https://drive.google.com/file/d/1oSPnGvlR8XSesjMn9LIsKJJohOvCpnPZ/view?usp=drive_link',
	},
	{
		label: 'MacOS x64',
		url: 'https://drive.google.com/file/d/1zNNlixLaqi6LXvxTg-dL6qdsE1P3awUy/view?usp=drive_link',
	},
	{
		label: 'Windows arm64',
		url: 'https://drive.google.com/file/d/17rM5l37nAEDd-QiB3Pj38bWO_fmyIJt8/view?usp=drive_link',
	},
	{
		label: 'Windows x64',
		url: 'https://drive.google.com/file/d/1ZekbnJeFf5xSI2quKZhfEPj-y3ILyzx8/view?usp=drive_link',
	},
];

export const READER_FILENAMES: Record<(typeof READER_LINKS)[number], string> = {
	[`${READER_ORIGIN}/hwinfo`]: 'hwinfo.xml',
	[`${READER_ORIGIN}/systeminfo`]: 'systeminfo.json',
	[`${READER_ORIGIN}/gpuz`]: 'gpuz.xml',
	[`${READER_ORIGIN}/pnp`]: 'pnp.json',
	[`${READER_ORIGIN}/aggreg`]: 'aggreg.json',
	[`${READER_ORIGIN}/ios`]: 'ios.json',
	[`${READER_ORIGIN}/android`]: 'android.json',
};

export const READER_MIME_TYPES: Record<(typeof READER_LINKS)[number], string> = {
	[`${READER_ORIGIN}/hwinfo`]: 'application/xml',
	[`${READER_ORIGIN}/systeminfo`]: 'application/json',
	[`${READER_ORIGIN}/gpuz`]: 'application/xml',
	[`${READER_ORIGIN}/pnp`]: 'application/json',
	[`${READER_ORIGIN}/aggreg`]: 'application/json',
	[`${READER_ORIGIN}/ios`]: 'application/json',
	[`${READER_ORIGIN}/android`]: 'application/json',
};

export const BATCH_STATUS_TRANSITION_TIMESTAMPS = ['createdAt', 'deliveredAt', 'checkedAt', 'checkedSnAt', 'testedAt'] as const;
export const BATCH_STATUS_TRANSITION_USERS = {
	createdAt: 'createdBy',
	deliveredAt: 'deliveredBy',
	checkedAt: 'checkedBy',
	checkedSnAt: 'checkedSnBy',
	testedAt: 'testedBy',
} as const;

export const UUID_REGEX = '[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}';

export const SERVICE_FLOWS = ['internal', 'external'] as const;

export const SHIPMENT_STATUS_NAMES = ['NEW', 'DISPATCHED', 'DELIVERED'] as const;

export const VAT_RATE = 1.21;

export const NO_MATCH_ATTRIBUTE = '_NO_MATCH_';

export const PHOTO_USAGE = {
	detail: 'Detail',
	delivery: 'Příjem',
	check: 'Kontrola',
	test: 'Test',
	defect: 'Závada',
	customerClaim: 'Zákaznická reklamace',
} as const;

export const PHOTO_USAGE_ENTITY = {
	batch: 'Várka',
	batchDefect: 'Vada várky',
	product: 'Produkt',
	productCode: 'Kód produktu',
	productDefect: 'Vada produktu',
	serviceCase: 'Servis',
	vendor: 'Dodavatel',
	warrantyClaim: 'Dod. reklamace',
	inventory: 'Inventura',
	productCosmeticDefect: 'Kosmetická vada',
	customerClaim: 'Zákaznická reklamace',
} as const;

export const UNIQUE_PRODUCT_PRICE_TYPES = ['BUY', 'DELIVERY', 'SELL', 'RECOMMENDED', 'STANDARD'] as const;
export const PRODUCT_PRICE_TYPES = [...UNIQUE_PRODUCT_PRICE_TYPES, 'DISCOUNT', 'SERVICE', 'CUSTOMER_CLAIM'] as const;

export const WARRANTY_TYPES = ['BUY', 'SELL', 'SERVICE'] as const;

export const PRINTER_LOCATIONS = ['DELIVERY', 'SERVICE', 'SHIPMENT', 'TESTING'] as const;

export const SYSTEM_USER_AUTH_ID = '0869a0b4-ecce-4b07-a6be-1ac237408b87';
// TODO: change the below with production one
export const PUBLIC_CUSTOMER_AUTHENTICATION_ID = 'c69017e0-b5bc-4b2f-b78f-abf638553f11';

const LAMBDA_PREFIX = '/lambda';
export const LAMBDA_ENV = {
	DDB_HOST: '',
	S3_HOST: '',
	SES_HOST: '',
	SQS_HOST: '',
	LAMBDA_HOST: '',
	NODE_PATH: '/opt/node_modules:/var/runtime/node_modules',
};
export const CURRENCY_RATES_LAMBDA_ENDPOINT = `${LAMBDA_PREFIX}/currency-rates`;
export const SHOPTET_ORDERS_SYNC_ENDPOINT = `${LAMBDA_PREFIX}/shoptet-orders-sync`;
export const SHOPTET_ORDERS_VERIFY_ENDPOINT = `${LAMBDA_PREFIX}/shoptet-orders-verify`;
export const SHOPTET_ORDERS_UPDATE_ENDPOINT = `${LAMBDA_PREFIX}/shoptet-orders-update`;
export const SHOPTET_ORDERS_CLEANUP_ENDPOINT = `${LAMBDA_PREFIX}/shoptet-orders-cleanup`;
export const SHOPTET_IMPORT_ATTRIBUTE_ENDPOINT = `${LAMBDA_PREFIX}/shoptet-import-attribute`;
export const SHOPTET_IMPORT_BRAND_ENDPOINT = `${LAMBDA_PREFIX}/shoptet-import-brand`;
export const SHOPTET_IMPORT_CATEGORY_ENDPOINT = `${LAMBDA_PREFIX}/shoptet-import-category`;
export const SHOPTET_IMPORT_RECYCLING_FEE_ENDPOINT = `${LAMBDA_PREFIX}/shoptet-import-recycling-fee`;
export const PRODUCT_PRICES_SYNC_ENDPOINT = `${LAMBDA_PREFIX}/product-prices-sync`;
export const WAREHOUSE_PICKUP_NOTIFY_ENDPOINT = `${LAMBDA_PREFIX}/warehouse-pickup-notify`;
export const WAREHOUSE_PICKUP_SYNC_ENDPOINT = `${LAMBDA_PREFIX}/warehouse-pickup-sync`;

export const siBlackList = [
	'default',
	'services',
	'processLoad',
	'inetChecksite',
	'get',
	'observe',
	'version',
	'powerShellStart',
	'powerShellRelease',
	// ^-- these have to be here
	'dockerImages',
	'dockerContainers',
	'dockerContainerStats',
	'dockerContainerProcesses',
	'dockerInfo',
	'dockerVolumes',
	'dockerAll',
	'vboxInfo',
	'bluetoothDevices',
	'fsSize',
	'fsOpenFiles',
	'fsStats',
	'networkStats',
	'getDynamicData',
	'getAllData',
	'versions',
	'time',
	'shell',
	'uuid',
	'cpuCurrentSpeed',
	'cpuTemperature',
	'currentLoad',
	'fullLoad',
	'blockDevices',
	'networkGatewayDefault',
	'networkConnections',
	'wifiNetworks',
	'wifiConnections',
	'processes',
	'users',
	'printer',
	'cpuFlags',
	'getStaticData',
	'bios',
	'cpuCache',
	'disksIO',
	'inetLatency',
	'usb',
	'audio',
] as const;

export const SHOPTET_CREDENTIALS = process.env['SHOPTET_CREDENTIALS']
	? process.env['SHOPTET_CREDENTIALS']
	: '697094-p-452303-o8qxd0twf94iib18omxhgooaxx7joky8';
export const SHOPTET_CREDENTIALS_SK = process.env['SHOPTET_CREDENTIALS_SK']
	? process.env['SHOPTET_CREDENTIALS_SK']
	: '343501-p-522764-kv5wpamokusg6oksq0t3xx3qzersqzmv';
export const SHOPTET_COUNTRIES = ['CZ', 'SK'] as const;

export const RECOMMENDED_PRICE_IMPORT_NAME = 'prodejní cena v czk s dph';
export const STANDARD_PRICE_IMPORT_NAME = 'standardní cena v czk s dph';

// Warehouses
export const WAREHOUSE_TYPE_NAMES = ['STOCK', 'EXPEDITION', 'TESTING', 'DEPOSIT', 'SERVICE', 'WARRANTY_CLAIM'] as const;
export const TO_TEST_SECTOR = 'K otestování';
export const TO_STOCK_SECTOR = 'K naskladnění';
export const TO_CLAIM_SECTOR = 'K reklamaci';
export const COPY_SECTOR = 'Z. Copy';
export const STORE_SECTOR = 'K.7. Prodejna';
export const EXPEDITION_SECTOR = 'Expe';
export const EXTERNAL_SERVICE_SECTOR = 'Externí servis';

const ONE_TO_TEN = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'] as const;
const ONE_TO_TWENTY = [...ONE_TO_TEN, '11', '12', '13', '14', '15', '16', '17', '18', '19', '20'] as const;
export const WAREHOUSE_RACKS = ONE_TO_TWENTY;
export const WAREHOUSE_SHELVES = ONE_TO_TWENTY;
export const WAREHOUSE_BOXES = ONE_TO_TEN;

export const WAREHOUSE_TASK_STATUSES = ['OPEN', 'CLOSED'] as const;
export const WAREHOUSE_TASK_TYPES = ['SHIFT', 'PICKUP', 'STORE', 'INVENTORY'] as const;

export const EMPTY_VALUE = '-';

export const ORDER_ITEM_TYPES = ['PRODUCT', 'SHIPPING', 'PAYMENT', 'DISCOUNT', 'SERVICE', 'GIFT', 'PRODUCT_SET'] as const;
export const ORDER_CANCEL_STATUSES = ['Stornována', 'Stornovaná', 'HC - Storno'];
export const ORDER_SUCCESS_STATUSES = ['Vyřízena', 'Vyřízená', 'HC - Objednávka byla doručena', 'Vyřízeno - pokladna'];
export const ORDER_STATUSES_FOR_WAREHOUSE_TASKS = ['Připravit', 'Odeslat', 'Odeslat M'];
export const ORDER_STATUS_PROCESSING = 'Zpracovává se';
export const ORDER_SENT_STATUSES = [...ORDER_SUCCESS_STATUSES, 'Odesláno', 'Odesláno-fakturace', 'Odesláno - kontrola', 'Vychystáno'];

export const BATCH_TYPES = ['NEW', 'USED'] as const;
export const ENVELOPE_TYPES = ['NEW', 'USED', 'AMOUNT'] as const;
export const PRODUCT_TYPES = ['NEW', 'REFURBISHED', 'RENEWED'] as const;

export const MATEJ_EMAIL = '<EMAIL>';
export const PETR_EMAIL = '<EMAIL>';

export const EMAIL_STOCK_REPORT_RECIPIENTS = [
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	MATEJ_EMAIL,
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	PETR_EMAIL,
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
];

export const ERROR_RECIPIENTS = [PETR_EMAIL, MATEJ_EMAIL];

export const DEFAULT_RANGE = {
	from: startOfWeek(new Date(), { weekStartsOn: 1 }),
	to: endOfWeek(new Date(), { weekStartsOn: 1 }),
};

export const SERVICE_TASK_RESOLUTION_SCOPE = ['serviceTask', 'vendorTask', 'full'] as const;

export const PRODUCT_DEFECT_SOURCE = ['TESTING', 'SERVICE_CASE', 'WARRANTY_CLAIM', 'CUSTOMER_CLAIM'] as const;

export const INVENTORY_STATUS_NAMES = ['OPEN', 'CLOSED'] as const;

export const BASE_INVENTORY_ITEM_STATUS_NAMES = ['PENDING', 'OK', 'ERROR'] as const;

export const INVENTORY_ITEM_STATUS_NAMES = [...BASE_INVENTORY_ITEM_STATUS_NAMES, 'NEW_ADDITION'] as const;

export const PRICE_ROUNDING_STRATEGIES = ['HIGH', 'LOW'] as const;

export const NOTIFICATION_TYPE_NAMES = ['SHOPTET_EXPORT', 'ENVELOPE_UPDATE', 'PRODUCT_PICKUP', 'MISSING_RECYCLING_FEE'] as const;

export const NOTIFICATION_DELIVERY_METHODS_NAMES = ['push', 'email'] as const;

export const PUBLIC_CUSTOMER_USER_NAME = 'Zákazník';
