import {
	type AttributeCategoryType,
	type AttributeValueType,
	type AuthenticationType,
	type BatchDeliveryType,
	type BatchFileType,
	type BatchStatus,
	type BatchStatusTransitionTimestamp,
	type BatchType,
	type CustomerClaimDeliveryMethod,
	type CustomerClaimFileType,
	type CustomerClaimHandlingMethod,
	type CustomerClaimStatus,
	type EnvelopeType,
	type InventoryItemStatus,
	type InventoryStatus,
	type NotificationType,
	type OrderItemType,
	type PaymentStatus,
	type PriceRoundingStrategy,
	type PrinterLocation,
	type PriorityLevel,
	type ProductFileType,
	type ProductPriceType,
	type ProductStatus,
	type ProductTaskStatus,
	type ProductType,
	type ServiceCaseStatus,
	type ServiceCaseType,
	type ServiceTaskType,
	type ShipmentRelatedStatus,
	type ShipmentStatus,
	type WarehouseTaskStatus,
	type WarehouseTaskType,
	type WarehouseType,
	type WarrantyClaimStatus,
	type WarrantyType,
} from './types';

const commonMessages = {
	AT_SUPPLIER: 'U dodavatele',
	ON_THE_WAY: 'Na cestě',
	EXTERNAL_STOCK: 'Externí uskladnění',
	TO_CHECK: 'Ke kontrole',
	TO_CHECK_SN: 'Ke kontrole SN',
	TO_TEST: 'K otestování',
};

export const BatchStatusMessage: Record<BatchStatus, string> = {
	...commonMessages,
	IMPORTING: 'Importuje se...',
	MARKETING: 'Marketing',
	CLOSED: 'Uzavřená',
};

const ShipmentRelatedStatusMessage: Record<ShipmentRelatedStatus, string> = {
	WAITING_FOR_SHIPMENT: 'Čeká na odeslání',
	SHIPMENT_DISPATCHED: 'Odesláno',
	SHIPMENT_DELIVERED: 'Doručeno',
};

export const ProductStatusMessage: Record<ProductStatus, string> = {
	...commonMessages,
	TESTED: 'Otestováno',
	MISSING: 'Chybí',
	SERVICE: 'Servis',
	WARRANTY_CLAIM: 'Dod. reklamace',
	STOCK: 'K naskladnění',
	FOR_SALE: 'V prodeji',
	RESERVED: 'Rezervováno',
	SOLD: 'Prodané',
	DEAD: 'Mrtvola',
	RETURNED: 'Vráceno dodavateli',
	AUTOPSY: 'Pitva',
	CUSTOMER_CLAIM: 'Zákaznická reklamace',
	LOST_FROM_INVENTORY: 'Ztraceno ve skladu',
	...ShipmentRelatedStatusMessage,
};

export const WarrantyTypesLabels: Record<WarrantyType, string> = {
	BUY: 'Dodavatelská',
	SELL: 'Prodejní',
	SERVICE: 'Servis',
};

export const PaymentStatusMessage: Record<PaymentStatus, string> = {
	INITIATED: 'Zadáno',
	PAID: 'Zaplaceno',
	UNPAID: 'Nezaplaceno',
};

export const PriorityMessage: Record<PriorityLevel, string> = {
	LOW: 'Nízká',
	MEDIUM: 'Střední',
	HIGH: 'Vysoká',
};

export const WarrantyBaselineMessage: Record<BatchStatusTransitionTimestamp, string> = {
	createdAt: 'Po vytvoření',
	deliveredAt: 'Po doručení',
	checkedAt: 'Po kontrole',
	checkedSnAt: 'Po kontrole SN',
	testedAt: 'Po testování',
};

export const ServiceTasksTypeMessage: Record<ServiceTaskType, string> = {
	SERVICE: 'Servis',
	TESTING: 'Při testování',
};

export const BatchDeliveryTypeMessage: Record<BatchDeliveryType, string> = {
	OWN: 'Vlastní',
	VENDOR: 'Dodavatel',
};

export const ServiceCaseStatusMessage: Record<ServiceCaseStatus, string> = {
	NEW: 'Nový',
	ASSIGNED_TO_INTERNAL_SERVICE: 'Přiřazen interní servis',
	ASSIGNED_TO_EXTERNAL_SERVICE: 'Přiřazen externí servis',
	SENT_TO_SERVICE_CENTER: 'Odesláno do servisu',
	OFFER_RECEIVED: 'Nabídka od servisu',
	OFFER_REJECTED: 'Nabídka odmítnuta',
	WAITING_FOR_PRODUCT: 'Čeká se na produkt',
	WAITING_FOR_REPAIR: 'Schváleno, čeká na opravu',
	WAITING_FOR_RETURN: 'Odmítnuto, čeká na vrácení',
	WAITING_FOR_BUYBACK: 'Nabídka k odkupu',
	CLOSED: 'Zavřeno',
};

export const ServiceCaseTypeMessage: Record<ServiceCaseType, string> = {
	FRONTEND: 'Frontend',
	BACKOFFICE: 'Backoffice',
};

export const WarrantyClaimStatusMessage: Record<WarrantyClaimStatus, string> = {
	NEW: 'Čeká na vyřízení',
	WAITING_FOR_VENDOR: 'V řešení s dodavatelem',
	SENT_TO_VENDOR: 'Zboží odesláno dodavateli',
	WAITING_FOR_RETURN: 'V externím servise',
	CLOSED: 'Uzavřená',
	REJECTED: 'Zamítnutá',
	REFUNDED: 'Vrácené peníze',
	DISCOUNT: 'Zlevněná',
	WAITING_FOR_REPAIR: 'Odesláno do servisu a čeká na opravu',
	RESOLVED_BY_VENDOR: 'Vyřešeno dodavatelem',
	TRADED_PIECE: 'Vyměněn kus',
	WAITING_FOR_DISCOUNT: 'Čeká na slevu',
};

export const CustomerClaimStatusMessage: Record<CustomerClaimStatus, string> = {
	NEW: 'Čeká na doručení',
	RECEIVED: 'Přijatá',
	PROCESSING: 'Zpracovává se',
	WAITING_FOR_SPARE_PART: 'Čeká na náhradní díl',
	SENT_TO_EXTERNAL_SERVICE: 'Odesláno do externího servisu',
	WAITING_FOR_RESOLUTION: 'Čeká na vyřešení',
	SENT_BACK: 'Odeslaná zpět',
	RESOLVED: 'Vyřízená',
	CANCELLED: 'Zrušená',
};

export const CustomerClaimFileTypeMap: Record<CustomerClaimFileType, string> = {
	creation: 'Z vytvoření reklamace',
	receipt: 'Přijetí reklamovaného produktu',
	final: 'Z vyřízení reklamace',
	regular: 'Ostatní',
};

export const CustomerClaimHandlingMethodMessage: Record<CustomerClaimHandlingMethod, string> = {
	EXCHANGE: 'Výměna',
	REPAIR: 'Oprava',
	REFUND: 'Vrácení peněz',
};

export const CustomerDeliveryMethodMessage: Record<CustomerClaimDeliveryMethod, string> = {
	IN_PERSON: 'Doručení na prodejnu',
	SHIPMENT: 'Zásilka přepravní společností',
};

export const CmpDeliveryMethodMessage: Record<CustomerClaimDeliveryMethod, string> = {
	IN_PERSON: 'Vyzvednu na prodejně',
	SHIPMENT: 'Poslat zpět',
};

export const ShipmentStatusMessage: Record<ShipmentStatus, string> = {
	NEW: 'Čeká na odeslání',
	DISPATCHED: 'Odesláno',
	DELIVERED: 'Doručeno',
};

export const AttributeCategoryTypeMessage: Record<AttributeCategoryType, string> = {
	default: 'Název',
	testing: 'Zobrazovat při testování',
	compare: 'Porovnávat při testování',
	mandatory: 'Povinné pro testování',
	envelope: 'Tvoří kartu produktu',
	name: 'Generuje název karty',
	export: 'Pro export',
	list: 'Zobrazovat ve výpisech',
	config: 'Testerský náhled',
	stock: 'Pro naskladnění',
	report: 'Pro e-mail report',
};

export const AttributeCategoryTypeTooltip: Record<AttributeCategoryType, string> = {
	default: '',
	testing: 'Tyto parametry se zobrazují při testování produktů.',
	compare:
		'Tyto parametry jsou na testování zobrazeny tučně. Porovnávají se s hodnotami z importu. Pokud nesedí, produkt pujde do dod. reklamace.',
	mandatory: 'Tyto parametry jsou na testování zobrazeny s hvězdičkou. Musí být vyplněny.',
	envelope: 'Unikátní kombinace hodnot parametrů vytváří karty produktů.',
	name: 'Z těchto parametrů se automaticky generuje název karty.',
	export: 'Tyto parametry se exportují do shoptetu a do excelových reportů. Pozor, do shoptetu se exportují pouze ty parametry, které jsou na kartě (tvoří kartu produktu).',
	list: 'Tyto parametry se zobrazují ve výpisech, např. na detailu várky nebo v seznamu produktů.',
	config: 'Tyto parametry slouží ke stručné identifikaci produktu na testu, vždycky by měl být zaškrtnutý výrobce a model a pak maximálně 5 dalších parametrů.',
	stock: 'Tyto parametry se zobrazují na stránce naskladnění.',
	report: 'Tyto parametry se zobrazují v těle e-mailového reportu po naskladnění.',
};

export const ProductTaskStatusMessage: Record<ProductTaskStatus, string> = {
	NEW: 'Nový',
	CLOSED: 'Zavřeno',
};

export const ProductPriceTypeMessage: Record<ProductPriceType, string> = {
	BUY: 'Nákup',
	DELIVERY: 'Doprava',
	DISCOUNT: 'Sleva',
	SELL: 'Prodejní',
	SERVICE: 'Servis',
	RECOMMENDED: 'Doporučená',
	STANDARD: 'Standardní',
	CUSTOMER_CLAIM: 'Zákaznická reklamace',
};

export const attributeValueTypeMap: Record<AttributeValueType, string> = {
	resolved: 'Aktuální hodnota',
	reader: 'Z vyčítače',
	import: 'Z importu',
	service: 'Ze servisu',
};

export const batchFileTypeMap: Record<BatchFileType, string> = {
	import: 'Z importu',
	delivery: 'Z příjmu',
	regular: '',
};

export const productFileTypeMap: Record<ProductFileType, string> = {
	import: 'Z importu',
	reader: 'Z vyčítače',
	regular: '',
};

export const PrinterLocationMessage: Record<PrinterLocation, string> = {
	DELIVERY: 'Příjem',
	SERVICE: 'Servis',
	SHIPMENT: 'Expedice',
	TESTING: 'Testování',
};

export const AuthTypeMessage: Record<AuthenticationType, string> = {
	'azure-ad': 'Microsoft účet',
	password: 'Heslo',
	verificationCode: 'QR kód',
};

export const WarehouseTypeNames: Record<WarehouseType, string> = {
	STOCK: 'Prodejní',
	EXPEDITION: 'Expedice',
	DEPOSIT: 'Depozit',
	SERVICE: 'Servis',
	TESTING: 'Test',
	WARRANTY_CLAIM: 'Dod. reklamace',
};

export const WarehouseTaskTypeNames: Record<WarehouseTaskType, string> = {
	INVENTORY: 'Inventura',
	PICKUP: 'Vyskladnění',
	SHIFT: 'Přesun',
	STORE: 'Naskladnění',
};

export const WarehouseTaskStatusNames: Record<WarehouseTaskStatus, string> = {
	OPEN: 'Otevřený',
	CLOSED: 'Hotový',
};

export const BatchTypeNames: Record<BatchType, string> = {
	NEW: 'Nová',
	USED: 'Použitá',
};

export const EnvelopeTypeNames: Record<EnvelopeType, string> = {
	NEW: 'Nová',
	USED: 'Použitá',
	AMOUNT: 'Množstevní',
};

export const OrderItemTypeNames: Record<OrderItemType, string> = {
	DISCOUNT: 'Sleva',
	PAYMENT: 'Platba',
	SHIPPING: 'Doprava',
	SERVICE: 'Servis',
	PRODUCT: 'Produkt',
	GIFT: 'Dárek',
	PRODUCT_SET: 'Sada produktů',
};

export const InventoryStatusMessage: Record<InventoryStatus, string> = {
	OPEN: 'Otevřená',
	CLOSED: 'Uzavřená',
};

export const InventoryItemStatusMessage: Record<InventoryItemStatus, string> = {
	PENDING: 'Čeká na skenování',
	OK: 'Ok',
	ERROR: 'Chyba',
	NEW_ADDITION: 'Nově přidáno',
};

export const PriceRoundingStrategyMessage: Record<PriceRoundingStrategy, string> = {
	HIGH: '490, 990',
	LOW: 'x20, x30, x40, x50, x60, x70, x80',
};

export const ProductTypeMessage: Record<ProductType, string> = {
	REFURBISHED: 'Repasovaný',
	NEW: 'Nové',
	RENEWED: 'Předváděcí',
};

export const NOTIFICATION_TITLES: Record<NotificationType, string> = {
	ENVELOPE_UPDATE: 'Tato karta potřebuje upřesnit',
	PRODUCT_PICKUP: 'Produkt nebyl zaskladněn',
	SHOPTET_EXPORT: 'Export do Shoptetu',
	MISSING_RECYCLING_FEE: 'Chybějící recyklační poplatky',
};
