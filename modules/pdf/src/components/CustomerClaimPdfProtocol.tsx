import { APP_URL, CustomerClaimHandlingMethodMessage, CustomerDeliveryMethodMessage } from '@pocitarna-nx-2023/config';
import { type CustomerClaim } from '@pocitarna-nx-2023/database';
import { formatCustomerClaimCode, formatDate, formatDateTime } from '@pocitarna-nx-2023/utils';
import { Link } from '@react-pdf/renderer';
import { type FC } from 'react';
import { Layout } from './Layout';
import { Section } from './ui/Section';
import { Table, TableCell, TableHeader, TableRow } from './ui/Table';
import { Text } from './ui/Text';
import { Muted, SubTitle, Title } from './ui/Typography';
import { View } from './ui/View';

type Props = {
	customerClaim: CustomerClaim;
};

export const CustomerClaimPdfProtocol: FC<Props> = ({ customerClaim }) => {
	const order = customerClaim.ecommerceOrderItem.ecommerceOrder;

	return (
		<Layout footer={<Text>Dokument vytvořen dne: {formatDateTime(new Date())}</Text>}>
			<Title>
				Reklamace čislo {formatCustomerClaimCode(customerClaim.code)} <Muted>{formatDate(customerClaim.createdAt)}</Muted>
			</Title>

			{/* Order */}
			<Section>
				<SubTitle>Objednávka</SubTitle>
				<Table>
					<TableHeader>
						<TableCell>Číslo</TableCell>
						<TableCell>Datum objednávky</TableCell>
					</TableHeader>

					<TableRow>
						<TableCell>{order.code}</TableCell>
						<TableCell>{formatDateTime(order.placedAt)}</TableCell>
					</TableRow>
				</Table>
			</Section>

			{/* Customer claim */}
			<Section>
				<SubTitle>Hlášení reklamace</SubTitle>
				<Table>
					<TableHeader>
						<TableCell>Popis závady</TableCell>
						<TableCell>Preferovaný způsob vyřízení reklamace</TableCell>
						<TableCell>Způsob doručení</TableCell>
					</TableHeader>

					<TableRow>
						<TableCell>{customerClaim.messages.map((message) => message.message).join('\n')}</TableCell>
						<TableCell>{CustomerClaimHandlingMethodMessage[customerClaim.handlingMethod]}</TableCell>
						<TableCell>{CustomerDeliveryMethodMessage[customerClaim.customerDeliveryMethod]}</TableCell>
					</TableRow>
				</Table>
			</Section>

			<Section>
				<SubTitle>Stav zboží při převzetí</SubTitle>

				<View className="mt-[10px]">
					<Text>
						Pro více informací navštivte{' '}
						<Link href={`${APP_URL}/public/customer-claim/${customerClaim.id}`}>stránku detailu reklamace</Link>
					</Text>
				</View>
			</Section>
		</Layout>
	);
};
