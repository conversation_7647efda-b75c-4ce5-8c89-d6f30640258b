import { type CustomerClaim, CustomerClaimController, FileController } from '@pocitarna-nx-2023/database';
import { renderToBuffer } from '@react-pdf/renderer';
import { CustomerClaimPdfProtocol } from './components/CustomerClaimPdfProtocol';

export const generateCustomerClaimProtocol = async (customerClaimId: CustomerClaim['id']) => {
	const customerClaim = await new CustomerClaimController().resolveRecord(customerClaimId);

	if (!customerClaim) throw new Error('Customer claim not found');

	const pdfBuffer = await renderToBuffer(<CustomerClaimPdfProtocol customerClaim={customerClaim} />);

	return pdfBuffer;
};

export const getCustomerClaimProtocolLink = async (customerClaimId: CustomerClaim['id']) => {
	const pdfBuffer = await generateCustomerClaimProtocol(customerClaimId);
	const pdfUint8Array = new Uint8Array(pdfBuffer);
	const savedFile = await new FileController().saveFile(pdfUint8Array, `Customer_Claim_Pdf_Protocol_${customerClaimId}.pdf`);

	return await new FileController().getFileDownloadLink(savedFile.id);
};
