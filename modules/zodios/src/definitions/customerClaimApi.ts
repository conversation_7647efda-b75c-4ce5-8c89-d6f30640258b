import { CUSTOMER_CLAIM_FILE_TYPE } from '@pocitarna-nx-2023/config';
import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { customerClaim, customerClaimCreate, customerClaimUpdate, errors, listProps } from '../entities';
import { code } from '../entities/code';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';

export const customerClaimApi = makeApi([
	{
		method: 'post',
		path: '/',
		alias: 'createCustomerClaim',
		response: apiResponse(z.string().uuid()),
		parameters: [{ name: 'customerClaim', type: 'Body', schema: customerClaimCreate }],
		errors,
	},
	{
		method: 'get',
		path: '/',
		alias: 'getCustomerClaims',
		response: apiPagedResponse(customerClaim),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/code/:customerClaimCodeId',
		alias: 'getCustomerClaimCode',
		response: apiResponse(code),
		parameters: [{ name: 'customerClaimCodeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:customerClaimId/protocol',
		alias: 'getCustomerClaimProtocol',
		response: z.any(),
		parameters: [{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:customerClaimId/accompanying-slip',
		alias: 'getCustomerClaimAccompanyingSlip',
		response: z.any(),
		parameters: [{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:customerClaimId',
		alias: 'getCustomerClaim',
		response: apiResponse(customerClaim),
		parameters: [{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/:customerClaimId',
		alias: 'updateCustomerClaim',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: customerClaimUpdate,
			},
		],
		errors,
	},
	{
		method: 'patch',
		path: '/:customerClaimId/files',
		alias: 'addFilesToCustomerClaim',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() },
			{ name: 'files', type: 'Body', schema: z.array(z.string()) },
			{ name: 'type', type: 'Query', schema: z.enum(CUSTOMER_CLAIM_FILE_TYPE) },
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:customerClaimId/files/:fileId',
		alias: 'deleteCustomerClaimFile',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() },
			{ name: 'fileId', type: 'Path', schema: z.string().uuid() },
		],
		errors,
	},
	{
		method: 'post',
		path: '/:customerClaimId/message',
		alias: 'sendCustomerClaimMessage',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() },
			{ name: 'customerClaim', type: 'Body', schema: z.object({ message: z.string().min(1) }) },
		],
		errors,
	},
]);
