import { BATCH_FILE_TYPE, CUSTOMER_CLAIM_FILE_TYPE, PRODUCT_FILE_TYPE } from '@pocitarna-nx-2023/config';
import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { entityWithFiles, errors, file, listProps, relatedFile, standaloneFile, updateSequenceBody } from '../entities';
import { apiResponse } from '../utils/wrapper';

export const filesApi = makeApi([
	{
		method: 'get',
		path: '/',
		alias: 'getFiles',
		response: apiResponse(z.array(standaloneFile)),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/:fileId/download',
		alias: 'downloadFile',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'fileId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:fileId/download/base64',
		alias: 'downloadBase64',
		response: z.string(),
		parameters: [{ name: 'fileId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'post',
		path: '/upload',
		alias: 'uploadFile',
		response: apiResponse(z.string()),
		parameters: [
			{
				name: 'file',
				type: 'Body',
				schema: z.instanceof(File),
			},
			{
				name: 'fileName',
				type: 'Query',
				schema: z.string(),
			},
		],
		errors,
	},
	{
		method: 'post',
		path: '/upload/base64',
		alias: 'uploadBase64',
		response: apiResponse(z.string()),
		parameters: [
			{
				name: 'file',
				type: 'Body',
				schema: z.object({
					id: z.string().optional(),
					type: entityWithFiles.optional(),
					photoSessionId: z.string().optional(),
					name: z.string(),
					data: z.string(),
				}),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/batch/:batchId',
		alias: 'getBatchFiles',
		response: apiResponse(z.array(relatedFile.extend({ type: z.enum(BATCH_FILE_TYPE) }))),
		parameters: [{ name: 'batchId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/batch/:batchId',
		alias: 'updateBatchFileSequence',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'batchId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: updateSequenceBody,
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/product/:productId',
		alias: 'getProductFiles',
		response: apiResponse(z.array(relatedFile.extend({ type: z.enum(PRODUCT_FILE_TYPE) }))),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/product/:productId',
		alias: 'updateProductFileSequence',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: updateSequenceBody,
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/batch/defect/:defectId',
		alias: 'getBatchDefectFiles',
		response: apiResponse(z.array(relatedFile)),
		parameters: [{ name: 'defectId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/batch-defect/:batchDefectId',
		alias: 'updateBatchDefectFileSequence',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'batchDefectId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: updateSequenceBody,
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/product/code/:productCodeId',
		alias: 'getProductCodeFiles',
		response: apiResponse(z.array(relatedFile)),
		parameters: [{ name: 'productCodeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/product/code/:productCodeId',
		alias: 'updateProductCodeFileSequence',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productCodeId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: updateSequenceBody,
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/product/defect/:defectId',
		alias: 'getProductDefectFiles',
		response: apiResponse(z.array(relatedFile)),
		parameters: [{ name: 'defectId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/product-defect/:productDefectId',
		alias: 'updateProductDefectFileSequence',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productDefectId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: updateSequenceBody,
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/serviceCase/:serviceCaseId',
		alias: 'getServiceCaseFiles',
		response: apiResponse(z.array(relatedFile)),
		parameters: [{ name: 'serviceCaseId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/service-case/:serviceCaseId',
		alias: 'updateServiceCaseFileSequence',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'serviceCaseId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: updateSequenceBody,
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/warrantyClaim/:warrantyClaimId',
		alias: 'getWarrantyClaimFiles',
		response: apiResponse(z.array(relatedFile)),
		parameters: [{ name: 'warrantyClaimId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/warranty-claim/:warrantyClaimId',
		alias: 'updateWarrantyClaimFileSequence',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'warrantyClaimId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: updateSequenceBody,
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/customer-claim/:customerClaimId',
		alias: 'getCustomerClaimFiles',
		response: apiResponse(z.array(relatedFile.extend({ type: z.enum(CUSTOMER_CLAIM_FILE_TYPE) }))),
		parameters: [
			{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() },
			{ name: 'type', type: 'Query', schema: z.enum(CUSTOMER_CLAIM_FILE_TYPE).optional() },
		],
		errors,
	},
	{
		method: 'patch',
		path: '/customer-claim/:customerClaimId',
		alias: 'updateCustomerClaimFileSequence',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: updateSequenceBody,
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/vendor/:vendorId',
		alias: 'getVendorFiles',
		response: apiResponse(z.array(relatedFile)),
		parameters: [{ name: 'vendorId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/vendor/:vendorId',
		alias: 'updateVendorFileSequence',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'vendorId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: updateSequenceBody,
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/inventory/:inventoryId',
		alias: 'getInventoryFiles',
		response: apiResponse(z.array(relatedFile)),
		parameters: [{ name: 'inventoryId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/inventory/:inventoryId',
		alias: 'updateInventoryFileSequence',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'inventoryId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: updateSequenceBody,
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/product/cosmetic-defect/:productCosmeticDefectId',
		alias: 'getProductCosmeticDefectFiles',
		response: apiResponse(z.array(relatedFile)),
		parameters: [{ name: 'productCosmeticDefectId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/product/cosmetic-defect/:productCosmeticDefectId',
		alias: 'updateProductCosmeticDefectFileSequence',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'productCosmeticDefectId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'body',
				type: 'Body',
				schema: updateSequenceBody,
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/product/bulk/defect',
		alias: 'getBulkProductDefectFiles',
		response: apiResponse(z.array(file)),
		parameters: [{ name: 'defectIds', type: 'Query', schema: z.array(z.string().uuid()) }],
		errors,
	},
	{
		method: 'get',
		path: '/photo-session/:photoSessionId',
		alias: 'getPhotoSessionFiles',
		response: apiResponse(z.array(file)),
		parameters: [
			{
				name: 'photoSessionId',
				type: 'Path',
				schema: z.string(),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/uploaded-files',
		alias: 'getUploadedFiles',
		response: apiResponse(z.array(file)),
		parameters: [
			{
				name: 'photoSessionId',
				type: 'Query',
				schema: z.string().optional(),
			},
			{
				name: 'photoIds',
				type: 'Query',
				schema: z.array(z.string()).optional(),
			},
		],
		errors,
	},
	{
		method: 'post',
		path: '/rotate',
		alias: 'rotateImageFile',
		response: apiResponse(file),
		parameters: [
			{
				name: 'body',
				type: 'Body',
				schema: z.object({
					fileId: z.string().uuid(),
					rotation: z.number(),
				}),
			},
		],
		errors,
	},
	{
		method: 'delete',
		path: '/',
		alias: 'deleteFiles',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'body',
				type: 'Body',
				schema: z.object({ ids: z.string().uuid().array() }),
			},
		],
		errors,
	},
]);
