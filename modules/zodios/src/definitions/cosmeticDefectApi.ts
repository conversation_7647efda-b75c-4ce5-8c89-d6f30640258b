import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { cosmeticDefect, cosmeticDefectCreate, errors, listProps, relatedFile, updateSequenceBody } from '../entities';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';

export const cosmeticDefectApi = makeApi([
	{
		method: 'post',
		path: '/',
		alias: 'createCosmeticDefect',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'grade', type: 'Body', schema: cosmeticDefectCreate }],
		errors,
	},
	{
		method: 'get',
		path: '/',
		alias: 'getCosmeticDefects',
		response: apiPagedResponse(cosmeticDefect),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/:cosmeticDefectId',
		alias: 'getCosmeticDefect',
		response: apiResponse(cosmeticDefect),
		parameters: [{ name: 'cosmeticDefectId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/:cosmeticDefectId',
		alias: 'updateCosmeticDefect',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'cosmeticDefectId', type: 'Path', schema: z.string().uuid() },
			{ name: 'grade', type: 'Body', schema: cosmeticDefectCreate.partial() },
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:cosmeticDefectId',
		alias: 'deleteCosmeticDefect',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'cosmeticDefectId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'post',
		path: '/relation',
		alias: 'establishCosmeticDefectRelation',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'body',
				type: 'Body',
				schema: z.object({
					cosmeticDefectId: z.string().uuid(),
					cosmeticAreaId: z.string().uuid(),
					productId: z.string().uuid().optional(),
					customerClaimId: z.string().uuid().optional(),
					isFix: z.boolean().optional(),
				}),
			},
		],
		errors,
	},
	{
		method: 'delete',
		path: '/relation',
		alias: 'removeCosmeticDefectRelation',
		response: apiResponse(z.boolean()),
		parameters: [
			{
				name: 'body',
				type: 'Body',
				schema: z.object({
					cosmeticDefectId: z.string().uuid(),
					productId: z.string().uuid().optional(),
					customerClaimId: z.string().uuid().optional(),
				}),
			},
		],
		errors,
	},
	{
		method: 'get',
		path: '/relation/:relatedEntityId/files',
		alias: 'getCosmeticDefectRelationFiles',
		response: apiResponse(z.array(relatedFile)),
		parameters: [
			{
				name: 'relatedEntityId',
				type: 'Path',
				schema: z.string().uuid(),
			},
			{
				name: 'relatedEntityType',
				type: 'Query',
				schema: z.enum(['product', 'customerClaim']),
			},
		],
		errors,
	},
	{
		method: 'patch',
		path: '/relation/:relatedEntityId/files',
		alias: 'updateCosmeticDefectRelationFileSequence',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'relatedEntityId', type: 'Path', schema: z.string().uuid() },
			{
				name: 'relatedEntityType',
				type: 'Query',
				schema: z.enum(['product', 'customerClaim']),
			},
			{
				name: 'body',
				type: 'Body',
				schema: updateSequenceBody,
			},
		],
		errors,
	},
]);
