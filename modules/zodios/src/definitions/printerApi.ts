import { makeApi } from '@zodios/core';
import { z } from 'zod';
import { errors, listProps, printer, printerCreate } from '../entities';
import { apiPagedResponse, apiResponse } from '../utils/wrapper';

export const printerApi = makeApi([
	{
		method: 'post',
		path: '/',
		alias: 'createPrinter',
		response: apiResponse(printer),
		parameters: [{ name: 'printer', type: 'Body', schema: printerCreate }],
		errors,
	},
	{
		method: 'get',
		path: '/batch-delivery/:batchId',
		alias: 'getPrintLabelsBatchDelivery',
		response: apiResponse(z.array(z.string())),
		parameters: [{ name: 'batchId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/product-envelope/bulk',
		alias: 'getBulkPrintLabelsProductEnvelope',
		response: apiResponse(z.array(z.string())),
		parameters: [{ name: 'productEnvelopeIds', type: 'Query', schema: z.array(z.string().uuid()) }],
		errors,
	},
	{
		method: 'get',
		path: '/batch/:batchId/small-test',
		alias: 'getBatchSmallTestPrintLabels',
		response: apiResponse(z.array(z.string())),
		parameters: [
			{ name: 'batchId', type: 'Path', schema: z.string().uuid() },
			{ name: 'productEnvelopeIds', type: 'Query', schema: z.array(z.string().uuid()) },
		],
		errors,
	},
	{
		method: 'get',
		path: '/product-envelope/:productEnvelopeId',
		alias: 'getPrintLabelsProductEnvelope',
		response: apiResponse(z.array(z.string())),
		parameters: [{ name: 'productEnvelopeId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/shipment/:shipmentId',
		alias: 'getPrintLabelsShipment',
		response: apiResponse(z.array(z.string())),
		parameters: [{ name: 'shipmentId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/service-case/:serviceCaseId/product/:productId',
		alias: 'getPrintLabelsService',
		response: apiResponse(z.array(z.string())),
		parameters: [
			{ name: 'serviceCaseId', type: 'Path', schema: z.string().uuid() },
			{ name: 'productId', type: 'Path', schema: z.string().uuid() },
		],
		errors,
	},
	{
		method: 'get',
		path: '/product/:productId',
		alias: 'getPrintLabelsProduct',
		response: apiResponse(z.array(z.string())),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/product/:productId/sn',
		alias: 'getPrintLabelsProductSn',
		response: apiResponse(z.array(z.string())),
		parameters: [{ name: 'productId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/warehousePosition/bulk',
		alias: 'getBulkPrintLabelsWarehousePosition',
		response: apiResponse(z.array(z.string())),
		parameters: [{ name: 'warehousePositionIds', type: 'Query', schema: z.array(z.string().uuid()) }],
		errors,
	},
	{
		method: 'get',
		path: '/warehousePosition/:warehousePositionId',
		alias: 'getPrintLabelsWarehousePosition',
		response: apiResponse(z.array(z.string())),
		parameters: [{ name: 'warehousePositionId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/customer-claim/:customerClaimId',
		alias: 'getPrintLabelsCustomerClaim',
		response: apiResponse(z.array(z.string())),
		parameters: [{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/',
		alias: 'getAllPrinters',
		response: apiPagedResponse(printer),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/:printerId',
		alias: 'getPrinter',
		response: apiResponse(printer),
		parameters: [{ name: 'printerId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:printerId/crt',
		alias: 'getPrinterCrt',
		response: z.string(),
		parameters: [{ name: 'printerId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:printerId/key',
		alias: 'getPrinterKey',
		response: z.string(),
		parameters: [{ name: 'printerId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'get',
		path: '/:printerId/nginx-conf',
		alias: 'getPrinterNginxConf',
		response: z.string(),
		parameters: [{ name: 'printerId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/:printerId',
		alias: 'updatePrinter',
		response: apiResponse(printer),
		parameters: [
			{ name: 'printerId', type: 'Path', schema: z.string().uuid() },
			{ name: 'printer', type: 'Body', schema: printerCreate },
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:printerId',
		alias: 'deletePrinter',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'printerId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'post',
		path: '/pdf',
		alias: 'exportLabels',
		response: apiResponse(z.string()),
		parameters: [{ name: 'body', type: 'Body', schema: z.object({ zpl: z.string() }) }],
		errors,
	},
]);
