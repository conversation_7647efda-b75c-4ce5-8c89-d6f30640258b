import { CUSTOMER_CLAIM_FILE_TYPE } from '@pocitarna-nx-2023/config';
import { makeApi } from '@zodios/core';
import { z } from 'zod';
import {
	customerClaim,
	customerClaimCreate,
	customerClaimMessage,
	errors,
	listProps,
	relatedCosmeticDefect,
	relatedDefect,
	relatedFile,
} from '../../entities';
import { apiResponse, historical } from '../../utils/wrapper';

export const publicCustomerClaimApi = makeApi([
	{
		method: 'get',
		path: '/',
		alias: 'getPublicCustomerClaims',
		response: apiResponse(z.array(customerClaim)),
		parameters: listProps,
		errors,
	},
	{
		method: 'get',
		path: '/:customerClaimId',
		alias: 'getPublicCustomerClaim',
		response: apiResponse(customerClaim),
		parameters: [{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'post',
		path: '/',
		alias: 'submitCustomerClaim',
		response: apiResponse(z.boolean()),
		parameters: [{ name: 'body', type: 'Body', schema: customerClaimCreate }],
		errors,
	},
	{
		method: 'get',
		path: '/:customerClaimId/files',
		alias: 'getPublicCustomerClaimFiles',
		response: apiResponse(z.array(relatedFile.extend({ type: z.enum(CUSTOMER_CLAIM_FILE_TYPE) }))),
		parameters: [{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
	{
		method: 'patch',
		path: '/:customerClaimId/files',
		alias: 'publicAddFilesToCustomerClaim',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() },
			{ name: 'files', type: 'Body', schema: z.array(z.string()) },
		],
		errors,
	},
	{
		method: 'delete',
		path: '/:customerClaimId/files/:fileId',
		alias: 'publicDeleteCustomerClaimFile',
		response: apiResponse(z.boolean()),
		parameters: [
			{ name: 'customerClaimId', type: 'Path', schema: z.string().uuid() },
			{ name: 'fileId', type: 'Path', schema: z.string().uuid() },
		],
		errors,
	},
	{
		method: 'get',
		path: '/:customerClaimId/history',
		alias: 'getPublicCustomerClaimHistory',
		response: apiResponse(
			historical({
				publicCustomerClaim: customerClaim,
				productDefect: relatedDefect,
				customerClaimMessage,
				fileCustomerClaim: relatedFile,
				cosmeticDefect: relatedCosmeticDefect,
			}),
		),
		parameters: [...listProps, { name: 'customerClaimId', type: 'Path', schema: z.string().uuid() }],
		errors,
	},
]);
