import { z } from 'zod';

export const customerClaimCosmeticDefect = z.object({
	id: z.string().uuid(),
	cosmeticDefectId: z.string().uuid(),
	customerClaimId: z.string().uuid(),
	cosmeticDefect: z.object({
		id: z.string().uuid(),
		name: z.string().min(1),
		pictureRequired: z.boolean().default(false),
		cosmeticAreaCosmeticDefects: z.array(z.object({ id: z.string().uuid(), cosmeticAreaId: z.string().uuid() })),
	}),
	cosmeticAreaId: z.string().uuid(),
});
