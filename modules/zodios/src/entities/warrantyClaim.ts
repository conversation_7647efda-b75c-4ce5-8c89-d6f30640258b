import { PRODUCT_STATUS_NAMES, WARRANTY_CLAIM_STATUS_NAMES } from '@pocitarna-nx-2023/config';
import { z } from 'zod';
import { code } from './code';
import { note } from './note';
import { user } from './user';
import { withActionDescription } from './withActionDescription';

export const warrantyClaimCreate = z.object({
	note: z.string().optional(),
	productDefects: z.array(z.object({ id: z.string().uuid() })),
	productId: z.string().uuid().optional(),
});

const warrantyClaimStatusEnum = z.enum(WARRANTY_CLAIM_STATUS_NAMES);

export const warrantyClaim = z.object({
	createdAt: z.coerce.date(),
	createdBy: user.nullish().default(null),
	createdById: z.string().uuid().nullish().default(null),
	id: z.string().uuid(),
	code,
	codeId: z.string().uuid(),
	notes: z.array(note).default([]),
	status: warrantyClaimStatusEnum,
	trackingCode: z.string(),
	vendorRMAIdentifier: z.string(),
	sourceServiceCaseId: z.string().uuid().nullish().default(null),
	sourceCustomerClaimId: z.string().uuid().nullish().default(null),
});

const warrantyClaimClosingParams = z.object({
	productStatus: z.enum(PRODUCT_STATUS_NAMES),
	shouldCreateServiceCase: z.literal(true).optional(),
	serviceTaskIdToClose: z.string().uuid().optional(),
});

const transitionBodyUnion = z.discriminatedUnion('status', [
	withActionDescription(z.object({ status: z.literal('NEW') })),
	withActionDescription(z.object({ status: z.literal('WAITING_FOR_VENDOR') })),
	withActionDescription(z.object({ status: z.literal('SENT_TO_VENDOR') })),
	withActionDescription(z.object({ status: z.literal('WAITING_FOR_RETURN') })),
	withActionDescription(z.object({ status: z.literal('CLOSED'), warrantyClaimClosingParams })),
	withActionDescription(z.object({ status: z.literal('REJECTED') })),
	withActionDescription(z.object({ status: z.literal('REFUNDED') })),
	withActionDescription(z.object({ status: z.literal('DISCOUNT') })),
	withActionDescription(
		z.object({
			status: z.literal('WAITING_FOR_REPAIR'),
			addressId: z.string().uuid(),
			contactId: z.string().uuid(),
			serviceTaskTypeId: z.string().uuid(),
			productDefectsIds: z.array(z.string().uuid()),
		}),
	),
	withActionDescription(z.object({ status: z.literal('WAITING_FOR_DISCOUNT') })),
	withActionDescription(z.object({ status: z.literal('RESOLVED_BY_VENDOR') })),
	withActionDescription(z.object({ status: z.literal('TRADED_PIECE'), productSN: z.string() })),
]);

export const warrantyClaimUpdate = z.object({
	trackingCode: z.string(),
	vendorRMAIdentifier: z.string(),
});

export const warrantyClaimTransitionBody = z.preprocess((input) => {
	if (typeof input === 'object' && input !== null && 'status' in input) {
		const status = (input as any).status;
		if (warrantyClaimStatusEnum.options.includes(status)) {
			return input;
		}
	} else if (typeof input === 'object' && input !== null && 'trackingCode' in input) {
		return input;
	}
	throw new Error('Invalid warranty claim status');
}, transitionBodyUnion);

export type WarrantyClaimTransitionBody = z.infer<typeof warrantyClaimTransitionBody>;
