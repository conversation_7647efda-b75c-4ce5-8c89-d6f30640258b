import {
	PRODUCT_STATUS_NAMES,
	SERVICE_CASE_STATUS_NAMES,
	SERVICE_CASE_TYPE_NAMES,
	SERVICE_TASK_STATUS_TYPES,
} from '@pocitarna-nx-2023/config';
import { z } from 'zod';
import { zodDecimal } from '../utils/zodDecimal';
import { code } from './code';
import { note } from './note';
import { serviceCenter } from './serviceCenter';
import { user } from './user';

export const serviceCaseCreate = z.object({
	note: z.string().optional(),
	productDefects: z.array(z.object({ id: z.string().uuid() })),
	productId: z.string().uuid().optional(),
	priority: z.number().optional(),
});

export const serviceCase = z.object({
	createdAt: z.coerce.date(),
	createdBy: user.nullish().default(null),
	createdById: z.string().uuid().nullish().default(null),
	id: z.string().uuid(),
	code,
	codeId: z.string().uuid(),
	notes: z.array(note).default([]),
	status: z.enum(SERVICE_CASE_STATUS_NAMES),
	type: z.enum(SERVICE_CASE_TYPE_NAMES),
	serviceCenter: serviceCenter.nullish().default(null),
	serviceCenterId: z.string().uuid().nullish().default(null),
	priority: z.number(),
	trackingCode: z.string(),
	sourceWarrantyClaimId: z.string().uuid().nullish().default(null),
	sourceCustomerClaimId: z.string().uuid().nullish().default(null),
});

const shipmentCreation = z.object({
	addressId: z.string().uuid(),
	contactId: z.string().uuid(),
	productsIds: z.array(z.string().uuid()).optional(),
});

const serviceTaskCreation = z.object({
	serviceTaskStatus: z.enum(SERVICE_TASK_STATUS_TYPES),
	serviceTaskTypeId: z.string().uuid(),
	serviceTaskPrice: zodDecimal(),
	productDefectsIds: z.array(z.string().uuid()),
	serviceTaskNote: z.string(),
	attributeValues: z.array(z.object({ attributeId: z.string().uuid(), attributeValueId: z.string().uuid() })),
});

const serviceTaskUpdates = z.object({
	serviceTaskId: z.string().uuid(),
	serviceTaskStatus: z.enum(SERVICE_TASK_STATUS_TYPES),
});

const serviceTaskDeletion = z.object({
	serviceTaskId: z.string().uuid(),
});

export const serviceCaseTransitionProps = z.object({
	status: z.enum(SERVICE_CASE_STATUS_NAMES),
	actionDescription: z.string().optional(),
	serviceCenterId: z.string().uuid().optional(),
	productStatus: z.enum(PRODUCT_STATUS_NAMES).optional(),
	serviceTaskCreation: serviceTaskCreation.optional(),
	serviceTaskUpdates: serviceTaskUpdates.optional(),
	shipmentCreation: shipmentCreation.optional(),
	serviceTaskDeletion: serviceTaskDeletion.optional(),
	shouldBuyBackProduct: z.literal(true).optional(),
	shouldCreateWarrantyClaim: z.literal(true).optional(),
});

export const serviceCaseUpdateProps = z.object({
	trackingCode: z.string(),
});

export type ServiceCaseTransitionProps = z.infer<typeof serviceCaseTransitionProps>;
