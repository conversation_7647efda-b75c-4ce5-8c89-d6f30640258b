import { z } from 'zod';
import { action } from './action';

export const file = z.object({ id: z.string().uuid(), name: z.string(), mime: z.string(), note: z.string(), preview: z.string() });
export const relatedFile = z.object({ id: z.string().uuid(), sequence: z.number(), file });
export const standaloneFile = file.extend({ action });

// Decided to keep this type, since it's useful generalisation
export type FileMetaData = z.infer<typeof file>;

export const entityWithFiles = z.enum([
	'batch',
	'batchDefect',
	'product',
	'productCode',
	'productDefect',
	'serviceCase',
	'vendor',
	'warrantyClaim',
	'inventory',
	'productCosmeticDefect',
	'customerClaim',
	'customerClaimCosmeticDefect',
]);
export type EntityWithFiles = z.infer<typeof entityWithFiles>;

export const updateSequenceBody = z.object({ fileId: z.string().uuid(), sequence: z.number().transform((num) => (num < 0 ? 0 : num)) });
