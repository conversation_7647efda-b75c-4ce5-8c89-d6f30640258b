import { mergeApis, type ZodiosResponseByAlias } from '@zodios/core';
import {
	addressApi,
	analyticsApi,
	attributeApi,
	authAdmin,
	azureApi,
	batchApi,
	batchHistoryApi,
	conditionalAttributeApi,
	contactApi,
	cosmeticAreaApi,
	cosmeticDefectApi,
	currencyApi,
	customerClaimApi,
	customerClaimCosmeticDefectApi,
	customerClaimHistoryApi,
	defectTypeApi,
	ecommerceOrderApi,
	errorApi,
	filesApi,
	gradeApi,
	healthcheckApi,
	invalidateApi,
	inventoryApi,
	noteApi,
	notificationApi,
	notificationTypeApi,
	outageApi,
	printerApi,
	productApi,
	productCategoryApi,
	productCodeApi,
	productCosmeticDefectApi,
	productDefectApi,
	productEnvelopeApi,
	productEnvelopeHistoryApi,
	productHistoryApi,
	productTaskApi,
	publicCustomerClaimApi,
	publicEcommerceOrderApi,
	publicProductApi,
	qrApi,
	recyclingFeeApi,
	savedFilterApi,
	scanApi,
	searchApi,
	serviceCase<PERSON>pi,
	serviceCaseHistoryApi,
	serviceCenterApi,
	serviceCenterHistoryApi,
	serviceTaskApi,
	serviceTaskTypeApi,
	shipmentApi,
	shipmentHistoryApi,
	shoptetAttributeApi,
	shoptetCategoryApi,
	shoptetOrderSyncApi,
	userApi,
	userNotificationTypeApi,
	vendorHistoryApi,
	vendorsApi,
	warehouseApi,
	warehousePositionApi,
	warehousePositionHistoryApi,
	warehouseTaskApi,
	warrantyClaimApi,
	warrantyClaimHistoryApi,
	webhookApi,
	wholesalePricingApi,
} from './definitions';

export type Api = ReturnType<
	typeof mergeApis<{
		'/': typeof healthcheckApi;
		'/address': typeof addressApi;
		'/analytics': typeof analyticsApi;
		'/attribute': typeof attributeApi;
		'/auth': typeof azureApi;
		'/auth/admin': typeof authAdmin;
		'/batch': typeof batchApi;
		'/conditional-attribute': typeof conditionalAttributeApi;
		'/contact': typeof contactApi;
		'/cosmetic-area': typeof cosmeticAreaApi;
		'/cosmetic-defect': typeof cosmeticDefectApi;
		'/currency': typeof currencyApi;
		'/customer-claim': typeof customerClaimApi;
		'/customer-claim/cosmetic-defect': typeof customerClaimCosmeticDefectApi;
		'/defect-type': typeof defectTypeApi;
		'/error': typeof errorApi;
		'/grade': typeof gradeApi;
		'/invalidate': typeof invalidateApi;
		'/inventory': typeof inventoryApi;
		'/note': typeof noteApi;
		'/printer': typeof printerApi;
		'/product': typeof productApi;
		'/product/cosmetic-defect': typeof productCosmeticDefectApi;
		'/product/envelope': typeof productEnvelopeApi;
		'/product/category': typeof productCategoryApi;
		'/product/code': typeof productCodeApi;
		'/product/defect': typeof productDefectApi;
		'/product/task': typeof productTaskApi;
		'/files': typeof filesApi;
		'/notification': typeof notificationApi;
		'/notification-type': typeof notificationTypeApi;
		'/order': typeof ecommerceOrderApi;
		'/outage': typeof outageApi;
		'/qr': typeof qrApi;
		'/recycling-fee': typeof recyclingFeeApi;
		'/saved-filter': typeof savedFilterApi;
		'/scan': typeof scanApi;
		'/search': typeof searchApi;
		'/service-case': typeof serviceCaseApi;
		'/service-center': typeof serviceCenterApi;
		'/service-task': typeof serviceTaskApi;
		'/service-task-type': typeof serviceTaskTypeApi;
		'/shipment': typeof shipmentApi;
		'/shoptet/attribute': typeof shoptetAttributeApi;
		'/shoptet/order-sync': typeof shoptetOrderSyncApi;
		'/shoptet/category': typeof shoptetCategoryApi;
		'/user': typeof userApi;
		'/user-notification-type': typeof userNotificationTypeApi;
		'/vendor': typeof vendorsApi;
		'/warehouse': typeof warehouseApi;
		'/warehouse/position': typeof warehousePositionApi;
		'/warehouse/task': typeof warehouseTaskApi;
		'/warranty-claim': typeof warrantyClaimApi;
		'/webhook': typeof webhookApi;
		'/wholesale-pricing': typeof wholesalePricingApi;

		// History routers
		'/history/batch': typeof batchHistoryApi;
		'/history/customer-claim': typeof customerClaimHistoryApi;
		'/history/product': typeof productHistoryApi;
		'/history/product-envelope': typeof productEnvelopeHistoryApi;
		'/history/service-case': typeof serviceCaseHistoryApi;
		'/history/service-center': typeof serviceCenterHistoryApi;
		'/history/shipment': typeof shipmentHistoryApi;
		'/history/vendor': typeof vendorHistoryApi;
		'/history/warehouse-position': typeof warehousePositionHistoryApi;
		'/history/warranty-claim': typeof warrantyClaimHistoryApi;

		// Public routers
		'/public/order': typeof publicEcommerceOrderApi;
		'/public/product': typeof publicProductApi;
		'/public/customer-claim': typeof publicCustomerClaimApi;
	}>
>;

export const api: Api = mergeApis({
	'/': healthcheckApi,
	'/address': addressApi,
	'/analytics': analyticsApi,
	'/attribute': attributeApi,
	'/auth': azureApi,
	'/auth/admin': authAdmin,
	'/batch': batchApi,
	'/conditional-attribute': conditionalAttributeApi,
	'/contact': contactApi,
	'/cosmetic-area': cosmeticAreaApi,
	'/cosmetic-defect': cosmeticDefectApi,
	'/currency': currencyApi,
	'/customer-claim': customerClaimApi,
	'/customer-claim/cosmetic-defect': customerClaimCosmeticDefectApi,
	'/defect-type': defectTypeApi,
	'/error': errorApi,
	'/invalidate': invalidateApi,
	'/inventory': inventoryApi,
	'/note': noteApi,
	'/notification-type': notificationTypeApi,
	'/printer': printerApi,
	'/product': productApi,
	'/product/envelope': productEnvelopeApi,
	'/product/category': productCategoryApi,
	'/product/code': productCodeApi,
	'/product/cosmetic-defect': productCosmeticDefectApi,
	'/product/defect': productDefectApi,
	'/product/task': productTaskApi,
	'/files': filesApi,
	'/grade': gradeApi,
	'/notification': notificationApi,
	'/order': ecommerceOrderApi,
	'/outage': outageApi,
	'/qr': qrApi,
	'/recycling-fee': recyclingFeeApi,
	'/saved-filter': savedFilterApi,
	'/scan': scanApi,
	'/search': searchApi,
	'/service-case': serviceCaseApi,
	'/service-center': serviceCenterApi,
	'/service-task': serviceTaskApi,
	'/service-task-type': serviceTaskTypeApi,
	'/shipment': shipmentApi,
	'/shoptet/attribute': shoptetAttributeApi,
	'/shoptet/category': shoptetCategoryApi,
	'/shoptet/order-sync': shoptetOrderSyncApi,
	'/user': userApi,
	'/user-notification-type': userNotificationTypeApi,
	'/vendor': vendorsApi,
	'/warehouse': warehouseApi,
	'/warehouse/position': warehousePositionApi,
	'/warehouse/task': warehouseTaskApi,
	'/warranty-claim': warrantyClaimApi,
	'/webhook': webhookApi,
	'/wholesale-pricing': wholesalePricingApi,

	// History routers
	'/history/batch': batchHistoryApi,
	'/history/customer-claim': customerClaimHistoryApi,
	'/history/product': productHistoryApi,
	'/history/product-envelope': productEnvelopeHistoryApi,
	'/history/service-case': serviceCaseHistoryApi,
	'/history/service-center': serviceCenterHistoryApi,
	'/history/shipment': shipmentHistoryApi,
	'/history/vendor': vendorHistoryApi,
	'/history/warehouse-position': warehousePositionHistoryApi,
	'/history/warranty-claim': warrantyClaimHistoryApi,

	// Public routers
	'/public/order': publicEcommerceOrderApi,
	'/public/product': publicProductApi,
	'/public/customer-claim': publicCustomerClaimApi,
});

export type ApiAliases = Api[number]['alias'];
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore - TS2321 - TS complains about not being able to index the type, but every response should have it
export type ApiBody<Alias extends ApiAliases, Frontend extends boolean = true> = ZodiosResponseByAlias<Api, Alias, Frontend>['_data'];
