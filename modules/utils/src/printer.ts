import { NOT_AVAILABLE } from '@pocitarna-nx-2023/config';
import { toDateString } from './formats';

export const getBatchLabels = (batchCode: string, palletCount: number) => {
	return Array.from({ length: palletCount }, (_, i) => i + 1)
		.toReversed()
		.map((palletIndex) => {
			return `
^XA
^CI28
^A0,80,80
^BY2,3,63
^FO25,25^FD${batchCode}^FS
^FO25,105^FDPaleta: ${palletIndex}/${palletCount}^FS
^FO25,144^BC,,N^FD${batchCode}^FS
^FO471,81,1^BQ,,3^FDLA,${batchCode}^FS
^XZ`;
		});
};

export const getProductEnvelopeLabels = (envelopes: { code: string; name: string; grade: string }[]) => {
	return envelopes.toReversed().map(({ code, name, grade }) => {
		return `
^XA
^CI28
^BY2,3,63
^A0,65,65
^FO25,25^FD${code}^FS
^A0,20,20
^FB396,3
^FO25,80^FD${name}^FS
^A0,65,65
^FB50,,,C
^FO471,87,1^FD${grade.slice(0, 1)}\\&^FS
${
	grade.length > 1
		? `^A0,25,25
^FO476,87,1^FD${grade.slice(1)}^FS`
		: ''
}
^FO25,144^BC,,N^FD${code}^FS
^FO471,81,1^BQ,,3^FDLA,${code}^FS
^FO471,25,1^GB50,50,2
^XZ`;
	});
};

export const getProductLabels = (productCodes: string[]) => {
	return productCodes.toReversed().map((code) => {
		return `
^XA
^CI28
^A0,80,80
^BY2,3,63
^FO25,25^FD${code}^FS
^FO25,144^BC,,N^FD${code}^FS
^FO471,81,1^BQ,,3^FDLA,${code}^FS
^XZ`;
	});
};

export const getSnLabels = (sns: string[][]) => {
	const height = 65;
	const labels = ['SN', 'IMEI1', 'IMEI2'];
	return sns.map(
		(sns) => `
^XA
^CI28
^BY2,3,30
${sns
	.map(
		(sn, index) => `
^FO20,${20 + index * height}^BC,,N^FD${sn}^FS
^A0,20,20
^FO20,${20 + 36 + index * height}^FD${labels?.[index] ?? NOT_AVAILABLE}: ${sn}^FS`,
	)
	.join('\n\n')}

^XZ`,
	);
};

export const getServiceLabels = (batchCode: string, serviceCodes: string[]) => {
	return serviceCodes.toReversed().map((serviceCode) => {
		return `^XA^MMT^PW406^LL1119^LS0^FT20,50^A0N,56,56^FH^CI28^FD${serviceCode}^FS^CI27^FT20,485^A0N,45,46^FH^CI28^FD${batchCode}^FS^CI27^FT20,409^BQN,2,11^FH^FDLA,${serviceCode}^FS^PQ1,0,1,Y^XZ`;
	});
};

export const getShipmentLabels = (shipmentCodes: string[]) => {
	return shipmentCodes.toReversed().map((shipmentCode) => {
		return `^XA^MMT^PW406^LL1119^LS0^FT20,50^A0N,56,56^FH^CI28^FD${shipmentCode}^FS^CI27^FT20,485^A0N,45,46^FH^CI28^FD${toDateString(new Date())}^FS^CI27^FT20,409^BQN,2,11^FH^FDLA,${shipmentCode}^FS^PQ1,0,1,Y^XZ`;
	});
};

export const getWarehousePositionLabels = (positions: { code: string; positionName: string }[]) => {
	return positions.toReversed().map(({ code, positionName }) => {
		return `
^XA
^CI28
^BY2,3,63
^A0,65,65
^FO25,85^FD${positionName}^FS
^FO25,144^BC,,N^FD${code}^FS
^FO471,81,1^BQ,,3^FDLA,${code}^FS
^XZ
`;
	});
};

export const getCustomerClaimLabels = (customerClaimCodes: string[]) => {
	return customerClaimCodes.toReversed().map((customerClaimCode) => {
		return `
^XA
^CI28
^BY2,3,63
^A0,65,65
^FO25,25^FD${customerClaimCode}^FS
^FO25,144^BC,,N^FD${customerClaimCode}^FS
^FO471,81,1^BQ,,3^FDLA,${customerClaimCode}^FS
^XZ
`;
	});
};
