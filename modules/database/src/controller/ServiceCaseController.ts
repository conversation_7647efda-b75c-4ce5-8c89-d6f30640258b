import {
	AFTER_SERVICE_PRODUCT_TARGET_STATUSES,
	EXTERNAL_SERVICE_SECTOR,
	isAfterServiceProductTargetStatus,
} from '@pocitarna-nx-2023/config';
import { filterUndefined, safeDivide, type SingleOrArray, uniques, uniquesBy } from '@pocitarna-nx-2023/utils';
import { type ListProps, type ServiceCaseTransitionProps } from '@pocitarna-nx-2023/zodios';
import { Decimal } from 'decimal.js';
import { type Repository } from 'typeorm';
import { type Batch, type File, type Product, ServiceCase, type ServiceCaseCode } from '../entity';
import { type QueryBuilderMiddleware } from '../types';
import { BaseController } from '../utils/BaseController';
import { listAll, listOne } from '../utils/list';
import {
	CustomerClaimController,
	FileServiceCaseController,
	NoteController,
	ProductController,
	ProductDefectController,
	ProductPriceController,
	ServiceCaseCodeController,
	ServiceTaskController,
	ShipmentController,
	WarehouseController,
	WarehousePositionController,
	WarrantyClaimController,
} from '.';

export class ServiceCaseController extends BaseController<ServiceCase> {
	constructor() {
		super(ServiceCase);
	}

	protected override getQueryBuilder(...middlewares: QueryBuilderMiddleware<ServiceCase>[]) {
		return super
			.getQueryBuilder(...middlewares)
			.innerJoinAndSelect('entity.code', 'code')
			.leftJoinAndSelect('entity.notes', 'note')
			.leftJoinAndSelect('note.createdBy', 'noteCreatedBy');
	}

	override async create(
		data: Omit<Parameters<Repository<ServiceCase>['save']>[0], 'code'> & { productId?: string; note?: string },
	): Promise<ServiceCase> {
		const { productId, productDefects, note, ...rest } = data;

		const code = await new ServiceCaseCodeController().create({});
		const serviceCase = await super.create({ ...rest, code });

		if (note) {
			await new NoteController().create({ content: note, serviceCase: { id: serviceCase.id } });
		}

		if (!serviceCase) throw new Error('Could not create service case');

		await Promise.all(
			(productDefects ?? []).map(async (productDefect) => {
				if (productDefect.id) {
					await new ProductDefectController().update(productDefect.id, { serviceCase: { id: serviceCase.id } });
				}
			}),
		);

		if (productId) {
			await new ProductController().update(productId, { status: 'SERVICE' });
		}

		return serviceCase;
	}

	async listWithAction(props: ListProps) {
		const queryBuilder = this.getQueryBuilder().innerJoin('entity.action', 'action');

		return this.list(props, queryBuilder);
	}

	async listWithProduct(props: ListProps, includeModel = false) {
		const qb = this.getQueryBuilder()
			.leftJoinAndSelect('entity.productDefects', 'productDefect')
			.leftJoinAndSelect('productDefect.product', 'product')
			.leftJoinAndSelect('product.code', 'productCode')
			.leftJoinAndSelect('product.productCategory', 'productCategory')
			.leftJoinAndSelect('product.productEnvelope', 'productEnvelope')
			.leftJoinAndSelect('productEnvelope.code', 'envelopeCode')
			.leftJoinAndSelect('productDefect.defectType', 'defectType');

		if (includeModel) {
			qb.leftJoinAndSelect('product.attributeValues', 'productAttributeValue')
				.leftJoinAndSelect('productAttributeValue.attributeValue', 'attributeValue')
				.leftJoinAndSelect('attributeValue.attribute', 'attribute')
				.andWhere('attribute.displayName = :modelDisplayName', { modelDisplayName: 'Model' })
				.andWhere('productAttributeValue.type = :type', { type: 'resolved' });
		}

		return this.list(props, qb);
	}

	async getServiceCasesByBatch(batchId: Batch['id']): Promise<ServiceCase[]> {
		const productDefects = await new ProductDefectController().findByBatch(batchId);

		const batchServiceCases = await Promise.all(
			productDefects.map(async (productDefect) => {
				const serviceCaseId = productDefect.serviceCaseId;
				if (!serviceCaseId) return null;

				const serviceCase = await this.findById(serviceCaseId);
				if (!serviceCase) return null;

				return serviceCase;
			}),
		);

		return uniquesBy(filterUndefined(batchServiceCases), 'id');
	}

	async getServiceCaseByProduct(productId: Product['id']) {
		const [[productDefect]] = await new ProductDefectController().list(
			listOne({ filter: { productId: { eq: productId }, serviceCaseId: { ne: null } } }),
		);
		if (!productDefect) return null;

		if (!productDefect.serviceCaseId) return null;

		return this.findById(productDefect.serviceCaseId);
	}

	async findByCode(code: ServiceCaseCode['code']) {
		const [result] = await this.list(listOne({ filter: { 'code.code': { eq: code } } }));
		if (result.length === 0) return null;
		return result[0];
	}

	listFiles(serviceCaseId: ServiceCase['id']) {
		return new FileServiceCaseController().list(listAll({ filter: { serviceCaseId: { eq: serviceCaseId } }, sort: ['sequence'] }));
	}

	async addFiles(serviceCaseId: ServiceCase['id'], fileIds: SingleOrArray<File['id'] | { id: File['id'] }>) {
		const [files] = await this.listFiles(serviceCaseId);
		const maxSequence = files.reduce((acc, file) => Math.max(acc, file.sequence), 0);
		const fileIdsToAdd = (Array.isArray(fileIds) ? fileIds : [fileIds])
			.map((fileId) => (typeof fileId === 'string' ? fileId : fileId.id))
			.filter((fileId) => !files.some((file) => file.fileId === fileId));
		return new FileServiceCaseController().link(serviceCaseId, fileIdsToAdd, { sequence: maxSequence + 1 });
	}

	async deleteFile(serviceCaseId: ServiceCase['id'], fileId: File['id'] | { id: File['id'] }) {
		return new FileServiceCaseController().unlink(serviceCaseId, fileId);
	}

	async findProduct(serviceCaseId: ServiceCase['id']) {
		const [[productDefect]] = await new ProductDefectController().list(listOne({ filter: { serviceCaseId: { eq: serviceCaseId } } }));
		if (!productDefect) return null;

		return new ProductController().findById(productDefect.productId);
	}

	async findProducts(serviceCaseId: ServiceCase['id']) {
		const [productDefects] = await new ProductDefectController().list(listAll({ filter: { serviceCaseId: { eq: serviceCaseId } } }));
		const productsIds = uniques(filterUndefined(productDefects.map((defect) => defect.productId)));

		if (productsIds.length < 1) return [];

		const [products] = await new ProductController().list(
			listAll({
				filter: {
					id: { eq: productsIds },
				},
			}),
		);

		return products;
	}

	async updateOnProductArrivalToServiceWarehouse(productId: Product['id']) {
		const [openProductDefects] = await new ProductDefectController().findByProduct(productId, {
			filter: { 'entity.serviceTaskId': { eq: null } },
		});

		if (openProductDefects.length < 1) return;

		// The product should be in only one open service case, hence the [0]
		const associatedServiceCaseId = filterUndefined(openProductDefects.map((item) => item.serviceCaseId))[0];
		if (!associatedServiceCaseId) return;

		const productsInServiceCase = await this.findProducts(associatedServiceCaseId);
		if (productsInServiceCase.length < 1) return;

		const warehousePositions = filterUndefined(productsInServiceCase.map((product) => product.warehousePosition));
		if (warehousePositions.length < 1) return;

		const [[serviceWarehouse]] = await new WarehouseController().list(
			listOne({
				filter: {
					type: { eq: 'SERVICE' },
				},
			}),
		);
		if (!serviceWarehouse) throw new Error('Service warehouse not available');

		const allProductsAreInServiceWarehouse = warehousePositions.every((position) => position.warehouse.id === serviceWarehouse.id);

		if (allProductsAreInServiceWarehouse) {
			await new ServiceCaseController().update(associatedServiceCaseId, { status: 'NEW' });
		}
	}

	async handleTransition(serviceCaseId: ServiceCase['id'], data: ServiceCaseTransitionProps) {
		const serviceCase = await this.findById(serviceCaseId);

		if (!serviceCase) throw new Error('No service case with this id');

		// In order service-case there can be multiple products associated to one case!
		const products = await this.findProducts(serviceCaseId);

		if (!products || products.length < 1) throw new Error('No product associated to this service case');

		const currentStatus = serviceCase.status;

		const {
			status,
			actionDescription,
			serviceCenterId,
			productStatus,
			serviceTaskCreation,
			serviceTaskUpdates,
			shipmentCreation,
			serviceTaskDeletion,
			shouldBuyBackProduct,
			shouldCreateWarrantyClaim,
		} = data;

		const shouldHandleBuyback = currentStatus === 'WAITING_FOR_BUYBACK' && status === 'CLOSED' && shouldBuyBackProduct;

		if (status === 'NEW') {
			const defects = await new ProductDefectController().findByServiceCaseId(serviceCaseId);
			if (defects && defects.length > 0) {
				const serviceTaskIds = uniques(filterUndefined(defects.flatMap((defect) => [defect.serviceTaskId, defect.vendorTaskId])));
				if (serviceTaskIds.length > 0) {
					const [productPrices] = await new ProductPriceController().list(
						listAll({ filter: { serviceTaskId: { eq: serviceTaskIds } } }),
					);

					await Promise.all(
						defects.map((defect) => new ProductDefectController().update(defect, { serviceTask: null, vendorTask: null })),
					);
					await new ServiceTaskController().delete(serviceTaskIds);
					await new ProductPriceController().delete(productPrices.map(({ id }) => id));
				}
			}
		}

		const updatedServiceCase = await this.update(serviceCase.id, {
			status,
			...(serviceCenterId != null ? { serviceCenter: { id: serviceCenterId } } : {}),
		});

		// Handle service-task creation
		const shouldCreateServiceTask =
			(currentStatus === 'ASSIGNED_TO_INTERNAL_SERVICE' && status === 'CLOSED') ||
			(currentStatus === 'SENT_TO_SERVICE_CENTER' && status === 'OFFER_RECEIVED') ||
			(currentStatus === 'WAITING_FOR_BUYBACK' && status === 'CLOSED');

		if (shouldCreateServiceTask && serviceTaskCreation !== undefined) {
			const { productDefectsIds, serviceTaskStatus, serviceTaskPrice, serviceTaskTypeId } = serviceTaskCreation;
			const pricePerDefect = safeDivide(serviceTaskPrice, productDefectsIds.length);

			await new ServiceTaskController().create({
				note: actionDescription ?? '',
				status: serviceTaskStatus,
				price: new Decimal(pricePerDefect),
				productDefects: productDefectsIds.map((id) => ({ id })),
				serviceTaskTypeId,
			});
		}

		// Handle service-task update
		if (currentStatus === 'WAITING_FOR_REPAIR' && status === 'CLOSED' && serviceTaskUpdates !== undefined) {
			const { serviceTaskId, serviceTaskStatus } = serviceTaskUpdates;
			await new ServiceTaskController().update(serviceTaskId, { status: serviceTaskStatus });
		}

		// Handle service-task deletion
		if (serviceTaskDeletion !== undefined) {
			const { serviceTaskId } = serviceTaskDeletion;
			await new ServiceTaskController().delete(serviceTaskId);
		}

		// Handle shipment creation
		if (currentStatus === 'ASSIGNED_TO_EXTERNAL_SERVICE' && status === 'SENT_TO_SERVICE_CENTER' && shipmentCreation !== undefined) {
			const { addressId, contactId, productsIds } = shipmentCreation;
			const productsToShip = productsIds
				? productsIds.map((id) => ({ id }))
				: (await this.findProducts(serviceCaseId)).map((product) => ({ id: product.id }));

			await new ShipmentController().create({
				address: { id: addressId },
				contact: { id: contactId },
				products: productsToShip,
			});

			const depositWarehouse = await new WarehouseController().findByType('DEPOSIT');

			if (!depositWarehouse) throw new Error('Deposit warehouse not available');

			const newPosition = await new WarehousePositionController().findOrCreate({
				warehouse: { id: depositWarehouse.id },
				sector: EXTERNAL_SERVICE_SECTOR,
			});

			await Promise.all(
				productsToShip.map(async (product) => {
					return await new ProductController().setWarehousePosition(product.id, newPosition.id);
				}),
			);
		}

		// Handle buyback
		if (shouldHandleBuyback) {
			await Promise.all(
				products.map(async (product) => {
					return await new ProductController().handleBuyBack(product.id);
				}),
			);
		}

		// Handle warrantyClaim creation (for abnormalities)
		if (status === 'CLOSED' && shouldCreateWarrantyClaim) {
			const warrantyClaim = await new WarrantyClaimController().create({
				note: serviceCase.notes.map((note) => note.content).join('\n'),
				sourceServiceCase: { id: serviceCaseId },
			});
			await this.update(serviceCase, { sourceWarrantyClaim: { id: warrantyClaim.id } });

			const productDefects = await new ProductDefectController().findByServiceCaseId(serviceCaseId);
			await Promise.all(
				(productDefects ?? []).map(async (productDefect) => {
					return await new ProductDefectController().update(productDefect.id, { warrantyClaim: { id: warrantyClaim.id } });
				}),
			);
		}

		// shouldBuyBackProduct updates the product status itself, no need to do it here
		if (productStatus && !shouldBuyBackProduct) {
			await Promise.all(
				products.map(async (product) => {
					const targetStatus = await this.determineProductTargetStatus(product.id, status, productStatus);
					return await new ProductController().update(product.id, { status: targetStatus });
				}),
			);
		}

		// Handle customer claim transition on close
		if (currentStatus !== 'CLOSED' && status === 'CLOSED' && serviceCase.sourceCustomerClaimId != null && !shouldCreateWarrantyClaim) {
			await new CustomerClaimController().update(serviceCase.sourceCustomerClaimId, {
				status: 'WAITING_FOR_RESOLUTION',
			});
		}

		return updatedServiceCase;
	}

	async determineProductTargetStatus(
		productId: Product['id'],
		serviceCaseStatus: ServiceCase['status'],
		requestedProductStatus: Product['status'],
	): Promise<Product['status']> {
		if (serviceCaseStatus !== 'CLOSED' || !isAfterServiceProductTargetStatus(requestedProductStatus)) return requestedProductStatus;

		const serviceTasks = await new ServiceTaskController().findByProduct(productId);
		const statusSet = new Set(serviceTasks.map((task) => task.serviceTaskType.afterServiceProductTargetStatus));

		return AFTER_SERVICE_PRODUCT_TARGET_STATUSES.find((status) => statusSet.has(status)) ?? requestedProductStatus;
	}
}
