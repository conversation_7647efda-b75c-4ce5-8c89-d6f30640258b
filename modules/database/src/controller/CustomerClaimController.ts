import { type CustomerClaimFileType } from '@pocitarna-nx-2023/config';
import { type SingleOrArray } from '@pocitarna-nx-2023/utils';
import { type Repository } from 'typeorm';
import { CustomerClaim, type File, type Product } from '../entity';
import { type QueryBuilderMiddleware } from '../types';
import { BaseController } from '../utils/BaseController';
import { listAll, listOne } from '../utils/list';
import {
	AddressController,
	ContactController,
	CustomerClaimMessageController,
	EcommerceOrderItemController,
	FileCustomerClaimController,
	NoteController,
	ProductController,
	ProductDefectController,
} from '.';
import { CustomerClaimCodeController } from './CustomerClaimCodeController';

export class CustomerClaimController extends BaseController<CustomerClaim> {
	constructor() {
		super(CustomerClaim);
	}

	protected override getQueryBuilder(...middlewares: QueryBuilderMiddleware<CustomerClaim>[]) {
		return super
			.getQueryBuilder(...middlewares)
			.innerJoinAndSelect('entity.code', 'code')
			.innerJoinAndSelect('entity.createdBy', 'createdBy')
			.leftJoinAndSelect('entity.notes', 'note')
			.leftJoinAndSelect('note.createdBy', 'noteCreatedBy')
			.leftJoinAndSelect('entity.messages', 'message')
			.leftJoinAndSelect('entity.warrantyClaims', 'warrantyClaims')
			.leftJoinAndSelect('warrantyClaims.code', 'warrantyClaimsCode')
			.leftJoinAndSelect('entity.serviceCases', 'serviceCases')
			.leftJoinAndSelect('serviceCases.code', 'serviceCasesCode')
			.innerJoinAndSelect('entity.address', 'address')
			.innerJoinAndSelect('entity.contact', 'contact')
			.innerJoinAndSelect('entity.ecommerceOrderItem', 'ecommerceOrderItem')
			.innerJoinAndSelect('ecommerceOrderItem.product', 'product')
			.leftJoinAndSelect('product.code', 'productCode')
			.innerJoinAndSelect('ecommerceOrderItem.ecommerceOrder', 'ecommerceOrder');
	}

	override async create(
		data: Parameters<Repository<CustomerClaim>['save']>[0] & {
			note?: string;
			message: string;
			files?: string[];
		},
	): Promise<CustomerClaim> {
		if (!data.contact || !data.address) throw new Error('Contact is required');
		const contactToUse = await new ContactController().findOrCreate(data.contact, 'strict');
		const addressToUse = await new AddressController().findOrCreate(data.address);
		const { productDefects, note, message, files, ...rest } = data;

		const code = await new CustomerClaimCodeController().create({});
		const customerClaim = await super.create({ ...rest, code, contact: { id: contactToUse.id }, address: { id: addressToUse.id } });
		const ecommerceOrderItem = data.ecommerceOrderItem?.id
			? await new EcommerceOrderItemController().findById(data.ecommerceOrderItem.id)
			: null;

		if (!ecommerceOrderItem || !ecommerceOrderItem.productId) throw new Error('Cannot create customer claim on this order item');

		await new ProductController().update(ecommerceOrderItem.productId, { status: 'CUSTOMER_CLAIM' });

		if (!customerClaim) throw new Error('Could not create customer claim');

		if (note) {
			await new NoteController().create({ content: note, customerClaim: { id: customerClaim.id } });
		}

		if (files && files.length > 0) {
			await new CustomerClaimController().addFiles(customerClaim.id, files, 'creation');
		}

		await new CustomerClaimMessageController().create({
			message,
			contact: { id: contactToUse.id },
			customerClaim: { id: customerClaim.id },
		});

		await Promise.all(
			(productDefects ?? []).map(async (productDefect) => {
				if (productDefect.id) {
					await new ProductDefectController().update(productDefect.id, { customerClaim: { id: customerClaim.id } });
				}
			}),
		);

		return customerClaim;
	}

	async getCustomerClaimByProduct(productId: Product['id']) {
		const [[productDefect]] = await new ProductDefectController().list(
			listOne({ filter: { productId: { eq: productId }, customerClaimId: { ne: null } } }),
		);
		if (!productDefect) return null;

		if (!productDefect.customerClaimId) return null;

		return this.findById(productDefect.customerClaimId);
	}

	listFiles(customerClaimId: CustomerClaim['id'], type?: CustomerClaimFileType) {
		return new FileCustomerClaimController().list(
			listAll({ filter: { customerClaimId: { eq: customerClaimId }, ...(type ? { type: { eq: type } } : {}) }, sort: ['sequence'] }),
		);
	}

	async addFiles(
		customerClaimId: CustomerClaim['id'],
		fileIds: SingleOrArray<
			| File['id']
			| {
					id: File['id'];
			  }
		>,
		type: CustomerClaimFileType,
	) {
		const [files] = await this.listFiles(customerClaimId);
		const maxSequence = files.reduce((acc, file) => Math.max(acc, file.sequence), 0);
		const fileIdsToAdd = (Array.isArray(fileIds) ? fileIds : [fileIds])
			.map((fileId) => (typeof fileId === 'string' ? fileId : fileId.id))
			.filter((fileId) => !files.some((file) => file.fileId === fileId));
		return new FileCustomerClaimController().link(customerClaimId, fileIdsToAdd, { sequence: maxSequence + 1, type });
	}

	async deleteFile(customerClaimId: CustomerClaim['id'], fileId: File['id'] | { id: File['id'] }) {
		return new FileCustomerClaimController().unlink(customerClaimId, fileId);
	}
}
