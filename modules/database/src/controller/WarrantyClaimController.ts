import { filterUndefined, type SingleOrArray, uniques, uniquesBy } from '@pocitarna-nx-2023/utils';
import { type ListProps, type WarrantyClaimTransitionBody } from '@pocitarna-nx-2023/zodios';
import { type Repository } from 'typeorm';
import { type Batch, type File, type Product, WarrantyClaim, type WarrantyClaimCode } from '../entity';
import { type QueryBuilderMiddleware } from '../types';
import { BaseController } from '../utils/BaseController';
import { listAll, listOne } from '../utils/list';
import {
	CustomerClaimController,
	FileWarrantyClaimController,
	NoteController,
	ProductController,
	ProductDefectController,
	ProductPriceController,
	ServiceCaseController,
	ServiceTaskController,
	ShipmentController,
	VendorController,
	WarrantyClaimCodeController,
} from '.';

export class WarrantyClaimController extends BaseController<WarrantyClaim> {
	constructor() {
		super(WarrantyClaim);
	}

	protected override getQueryBuilder(...middlewares: QueryBuilderMiddleware<WarrantyClaim>[]) {
		return super
			.getQueryBuilder(...middlewares)
			.innerJoinAndSelect('entity.code', 'code')
			.leftJoinAndSelect('entity.notes', 'note')
			.leftJoinAndSelect('note.createdBy', 'noteCreatedBy');
	}

	override async create(
		data: Omit<Parameters<Repository<WarrantyClaim>['save']>[0], 'code'> & { productId?: string; note?: string },
	): Promise<WarrantyClaim> {
		const { productId, productDefects, note, ...rest } = data;
		const code = await new WarrantyClaimCodeController().create({});
		const warrantyClaim = await super.create({ ...rest, code });

		if (note) {
			await new NoteController().create({ content: note, warrantyClaim: { id: warrantyClaim.id } });
		}

		await Promise.all(
			(productDefects ?? []).map(async (productDefect) => {
				if (productDefect.id) {
					await new ProductDefectController().update(productDefect.id, { warrantyClaim: { id: warrantyClaim.id } });
				}
			}),
		);

		if (productId) {
			await new ProductController().update(productId, { status: 'WARRANTY_CLAIM' });
		}

		return warrantyClaim;
	}

	async listWithAction(props: ListProps) {
		const queryBuilder = this.getQueryBuilder().innerJoin('entity.action', 'action');

		return this.list(props, queryBuilder);
	}

	async listWithProduct(props: ListProps, includeModel = false) {
		const qb = this.getQueryBuilder()
			.leftJoinAndSelect('entity.productDefects', 'productDefect')
			.leftJoinAndSelect('productDefect.product', 'product')
			.leftJoinAndSelect('product.batch', 'batch')
			.leftJoinAndSelect('product.code', 'productCode')
			.leftJoinAndSelect('product.warranty', 'buyWarranty', 'buyWarranty.type = :warrantyType', { warrantyType: 'BUY' })
			.leftJoinAndSelect('product.productCategory', 'productCategory')
			.leftJoinAndSelect('product.productEnvelope', 'productEnvelope')
			.leftJoinAndSelect('productEnvelope.code', 'envelopeCode')
			.leftJoinAndSelect('productDefect.defectType', 'defectType');

		if (includeModel) {
			qb.leftJoinAndSelect('product.attributeValues', 'productAttributeValue')
				.leftJoinAndSelect('productAttributeValue.attributeValue', 'attributeValue')
				.leftJoinAndSelect('attributeValue.attribute', 'attribute')
				.andWhere('attribute.displayName = :modelDisplayName', { modelDisplayName: 'Model' })
				.andWhere('productAttributeValue.type = :type', { type: 'resolved' });
		}

		return this.list(props, qb);
	}

	async findProduct(warrantyClaimId: WarrantyClaim['id']) {
		const [[referenceDefect]] = await new ProductDefectController().list(
			listOne({
				filter: {
					'entity.warrantyClaimId': {
						eq: warrantyClaimId,
					},
				},
			}),
		);

		if (!referenceDefect) return null;
		return await new ProductController().findById(referenceDefect.productId);
	}

	async getWarrantyClaimsByBatch(batchId: Batch['id']): Promise<WarrantyClaim[]> {
		const productDefects = await new ProductDefectController().findByBatch(batchId);

		const batchWarrantyClaims = await Promise.all(
			productDefects.map(async (productDefect) => {
				const warrantyClaimId = productDefect.warrantyClaimId;
				if (!warrantyClaimId) return null;

				const warrantyClaim = await this.findById(warrantyClaimId);
				if (!warrantyClaim) return null;

				return warrantyClaim;
			}),
		);

		return uniquesBy(filterUndefined(batchWarrantyClaims), 'id');
	}

	async getWarrantyClaimByProduct(productId: Product['id']) {
		const [[productDefect]] = await new ProductDefectController().list(
			listOne({ filter: { productId: { eq: productId }, warrantyClaimId: { ne: null } } }),
		);
		if (!productDefect) return null;

		if (!productDefect.warrantyClaimId) return null;

		return this.findById(productDefect.warrantyClaimId);
	}

	async findByCode(code: WarrantyClaimCode['code']) {
		const [result] = await this.list(listOne({ filter: { 'code.code': { eq: code } } }));
		if (result.length === 0) return null;
		return result[0];
	}

	listFiles(warrantyClaimId: WarrantyClaim['id']) {
		return new FileWarrantyClaimController().list(
			listAll({ filter: { warrantyClaimId: { eq: warrantyClaimId } }, sort: ['sequence'] }),
		);
	}

	async addFiles(warrantyClaimId: WarrantyClaim['id'], fileIds: SingleOrArray<File['id'] | { id: File['id'] }>) {
		const [files] = await this.listFiles(warrantyClaimId);
		const maxSequence = files.reduce((acc, file) => Math.max(acc, file.sequence), 0);
		const fileIdsToAdd = (Array.isArray(fileIds) ? fileIds : [fileIds])
			.map((fileId) => (typeof fileId === 'string' ? fileId : fileId.id))
			.filter((fileId) => !files.some((file) => file.fileId === fileId));
		return new FileWarrantyClaimController().link(warrantyClaimId, fileIdsToAdd, { sequence: maxSequence + 1 });
	}

	async deleteFile(warrantyClaimId: WarrantyClaim['id'], fileId: File['id'] | { id: File['id'] }) {
		return new FileWarrantyClaimController().unlink(warrantyClaimId, fileId);
	}

	async handleTransition(warrantyClaimId: WarrantyClaim['id'], data: WarrantyClaimTransitionBody) {
		const product = await this.findProduct(warrantyClaimId);
		if (!product) throw new Error('No product associated to this claim');

		// Used for reset
		if (data.status === 'NEW') {
			await new ProductController().update(product.id, { status: 'WARRANTY_CLAIM' });
			const defects = await new ProductDefectController().findByWarrantyClaimId(warrantyClaimId);
			if (defects && defects.length > 0) {
				const serviceTaskIds = uniques(filterUndefined(defects.flatMap((defect) => [defect.serviceTaskId, defect.vendorTaskId])));
				if (serviceTaskIds.length > 0) {
					const [productPrices] = await new ProductPriceController().list(
						listAll({ filter: { serviceTaskId: { eq: serviceTaskIds } } }),
					);

					await Promise.all(
						defects.map((defect) => new ProductDefectController().update(defect, { serviceTask: null, vendorTask: null })),
					);
					await new ServiceTaskController().delete(serviceTaskIds);
					await new ProductPriceController().delete(productPrices.map(({ id }) => id));
				}
			}

			return this.update(warrantyClaimId, { status: 'NEW' });
		}

		if (data.status === 'WAITING_FOR_REPAIR') {
			const { addressId, contactId, serviceTaskTypeId, productDefectsIds } = data;

			await new ServiceTaskController().create({
				note: '',
				serviceTaskTypeId,
				productDefects: productDefectsIds.map((id) => ({ id })),
			});

			await new ShipmentController().create({
				contact: { id: contactId },
				address: { id: addressId },
				products: [{ id: product.id }],
			});

			await new ProductController().update(product.id, { status: 'WAITING_FOR_SHIPMENT' });
			return this.update(warrantyClaimId, { status: 'WAITING_FOR_REPAIR' });
		}

		if (data.status === 'REFUNDED') {
			const warrantyClaim = await this.update(warrantyClaimId, {
				...data,
				status: 'CLOSED',
			});

			await new ProductController().addSalePrice(product.id, 0);
			const currentBuyPrice = await new ProductPriceController().findOrCreate({ productId: product.id, priceType: 'BUY' });
			await new ProductPriceController().create({
				product: { id: product.id },
				type: 'DISCOUNT',
				value: currentBuyPrice.value,
			});
			await new ProductController().update(product.id, { status: 'WAITING_FOR_SHIPMENT' });
			const batch = await new ProductController().getBatch(product.id);
			if (!batch) throw new Error('No batch');
			if (!batch.vendorId) throw new Error('No vendor');

			const vendor = await new VendorController().findById(batch.vendorId);
			if (!vendor) throw new Error('No vendor');

			await new ShipmentController().create({
				products: [{ id: product.id }],
				address: { id: vendor.address?.id },
				contact: { id: vendor.contact?.id },
			});

			return warrantyClaim;
		}

		if (data.status === 'TRADED_PIECE') {
			const { productSN } = data;
			await new ProductController().update(product.id, { sn: productSN.toUpperCase() });
			return this.update(warrantyClaimId, { status: 'TRADED_PIECE' });
		}

		if (data.status === 'CLOSED') {
			const warrantyClaim = await this.resolveRecord(warrantyClaimId);
			const { productStatus, shouldCreateServiceCase, serviceTaskIdToClose } = data.warrantyClaimClosingParams;
			const productDefects = await new ProductDefectController().findByWarrantyClaimId(warrantyClaimId);
			const batch = await new ProductController().getBatch(product.id);
			const vendor = batch?.vendorId ? await new VendorController().findById(batch.vendorId) : null;
			const vendorUncoveredDefectTypes = vendor?.defectTypes?.map(({ defectType }) => defectType.id) ?? [];
			const coveredProductDefects = (productDefects ?? []).filter(
				(defect) => !vendorUncoveredDefectTypes.includes(defect.defectType.id),
			);

			if (coveredProductDefects.length > 0 && coveredProductDefects.some((defect) => defect.vendorTaskId == null)) {
				throw new Error("Some defects associated to this claim don't have a vendor task assigned. Cannot close warranty claim.");
			}

			await new ProductController().update(product.id, { status: productStatus });

			// Handle customer claim transition on close
			if (warrantyClaim.sourceCustomerClaimId != null && !shouldCreateServiceCase) {
				await new CustomerClaimController().update(warrantyClaim.sourceCustomerClaimId, {
					status: 'WAITING_FOR_RESOLUTION',
				});
			}

			// Service-case flow
			if (shouldCreateServiceCase) {
				if (productDefects?.every((defect) => defect.serviceTask?.status === 'CLOSED')) {
					throw new Error('All defects are already solved. Cannot move to service');
				}

				const note = 'actionDescription' in data && typeof data.actionDescription === 'string' ? data.actionDescription : '';
				const serviceCase = await new ServiceCaseController().create({ note, sourceWarrantyClaim: { id: warrantyClaimId } });

				if (serviceCase) {
					const [warrantyClaimFiles] = await this.listFiles(warrantyClaimId);

					await new ServiceCaseController().addFiles(
						serviceCase.id,
						warrantyClaimFiles.map((file) => file.fileId),
					);

					await Promise.all(
						(productDefects ?? []).map((productDefect) =>
							new ProductDefectController().update(productDefect.id, { serviceCase: { id: serviceCase.id } }),
						),
					);
				}

				// Handle service-task closing
				if (serviceTaskIdToClose) {
					await new ServiceTaskController().update(serviceTaskIdToClose, {
						status: 'CLOSED',
					});
				}

				return this.update(warrantyClaimId, {
					status: 'CLOSED',
					...(serviceCase ? { sourceServiceCase: { id: serviceCase.id } } : {}),
				});
			}
		}

		return this.update(warrantyClaimId, { ...data });
	}
}
