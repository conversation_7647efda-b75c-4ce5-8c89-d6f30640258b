import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { type CustomerClaim, FileCustomerClaimHistory } from '../../entity';
import { type QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class FileCustomerClaimHistoryController extends BaseHistoryController<FileCustomerClaimHistory> {
	constructor() {
		super(FileCustomerClaimHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<FileCustomerClaimHistory>[]) {
		return super.getQueryBuilder(...middlewares).innerJoinAndSelect('entity.file', 'file');
	}

	async listByCustomerClaimId(customerClaimId: CustomerClaim['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		return this.listQuery({ ...props, filter: { ...props?.filter, customerClaimId: { eq: customerClaimId } } }, queryBuilder);
	}
}
