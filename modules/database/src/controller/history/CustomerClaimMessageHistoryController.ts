import type { ListProps } from '@pocitarna-nx-2023/zodios';
import { type CustomerClaim, CustomerClaimMessageHistory } from '../../entity';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class CustomerClaimMessageHistoryController extends BaseHistoryController<CustomerClaimMessageHistory> {
	constructor() {
		super(CustomerClaimMessageHistory);
	}

	async listByCustomerClaimId(id: CustomerClaim['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		const safeProps = props ?? {};
		return this.listQuery({ ...safeProps, filter: { ...safeProps?.filter, customerClaimId: { eq: id } } }, queryBuilder);
	}
}
