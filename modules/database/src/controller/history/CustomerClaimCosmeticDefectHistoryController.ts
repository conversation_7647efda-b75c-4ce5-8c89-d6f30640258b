import { type ListProps } from '@pocitarna-nx-2023/zodios';
import { type CustomerClaim, CustomerClaimCosmeticDefectHistory } from '../../entity';
import { type QueryBuilderMiddleware } from '../../types';
import { BaseHistoryController } from '../../utils/BaseHistoryController';

export class CustomerClaimCosmeticDefectHistoryController extends BaseHistoryController<CustomerClaimCosmeticDefectHistory> {
	constructor() {
		super(CustomerClaimCosmeticDefectHistory);
	}

	override getQueryBuilder(...middlewares: QueryBuilderMiddleware<CustomerClaimCosmeticDefectHistory>[]) {
		return super
			.getQueryBuilder(...middlewares)
			.innerJoinAndSelect('entity.cosmeticDefect', 'cosmeticDefect')
			.innerJoinAndSelect('entity.cosmeticArea', 'cosmeticArea');
	}

	async listByCustomerClaimId(customerClaimId: CustomerClaim['id'], props?: ListProps, queryBuilder = this.getQueryBuilder()) {
		return this.listQuery({ ...props, filter: { ...props?.filter, customerClaimId: { eq: customerClaimId } } }, queryBuilder);
	}
}
