import { type CustomerClaimDeliveryMethod, type CustomerClaimHandlingMethod, type CustomerClaimStatus } from '@pocitarna-nx-2023/config';
import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { Address } from './Address';
import { Contact } from './Contact';
import { CustomerClaimCode } from './CustomerClaimCode';
import { CustomerClaimCosmeticDefect } from './CustomerClaimCosmeticDefect';
import { CustomerClaimMessage } from './CustomerClaimMessage';
import { EcommerceOrderItem } from './EcommerceOrderItem';
import { FileCustomerClaim } from './File';
import { InventoryItem } from './InventoryItem';
import { Note } from './Note';
import { ProductDefect } from './ProductDefect';
import { ServiceCase } from './ServiceCase';
import { User } from './User';
import { WarrantyClaim } from './WarrantyClaim';

@Entity({ name: 'customerClaim' })
export class CustomerClaim extends CommonEntity {
	@ManyToOne(() => User, (user) => user.createdCustomerClaims)
	@Index()
	createdBy: User | null;
	@Column({ type: 'uuid', nullable: true })
	createdById: string | null;

	@Column({ type: 'timestamptz', default: null })
	@Index()
	receivedAt: Date | null;

	@Column({ type: 'timestamptz', default: null })
	@Index()
	acceptedAt: Date | null;

	@Column({ type: 'timestamptz', default: null })
	@Index()
	rejectedAt: Date | null;

	@Column({ type: 'varchar', default: 'NEW' })
	@Index()
	status: CustomerClaimStatus;

	@Column({ type: 'varchar' })
	@Index()
	handlingMethod: CustomerClaimHandlingMethod;

	@Column({ type: 'varchar' })
	@Index()
	customerDeliveryMethod: CustomerClaimDeliveryMethod;

	@Column({ type: 'varchar' })
	@Index()
	cmpDeliveryMethod: CustomerClaimDeliveryMethod;

	@OneToMany(() => ProductDefect, (productDefect) => productDefect.customerClaim)
	productDefects: ProductDefect[];

	@OneToMany(() => Note, (note) => note.customerClaim)
	notes: Note[];

	@OneToMany(() => InventoryItem, (inventoryItem) => inventoryItem.customerClaim)
	inventoryItems: InventoryItem[];

	@OneToMany(() => FileCustomerClaim, (fileCustomerClaim) => fileCustomerClaim.customerClaim)
	files: FileCustomerClaim[];

	@OneToMany(() => CustomerClaimMessage, (customerClaimMessage) => customerClaimMessage.customerClaim)
	messages: CustomerClaimMessage[];

	@OneToMany(() => CustomerClaimCosmeticDefect, (customerClaimCosmeticDefect) => customerClaimCosmeticDefect.customerClaim)
	customerClaimCosmeticDefects: CustomerClaimCosmeticDefect[];

	@OneToMany(() => WarrantyClaim, (warrantyClaim) => warrantyClaim.sourceCustomerClaim)
	warrantyClaims: WarrantyClaim[];

	@OneToMany(() => ServiceCase, (serviceCase) => serviceCase.sourceCustomerClaim)
	serviceCases: ServiceCase[];

	@ManyToOne(() => CustomerClaimCode, (code) => code.customerClaim, { onDelete: 'CASCADE' })
	@Index()
	code: CustomerClaimCode;
	@Column({ type: 'uuid' })
	codeId: string;

	@ManyToOne(() => Contact, (contact) => contact.customerClaims, { onDelete: 'SET NULL' })
	@Index()
	contact: Contact;
	@Column({ type: 'uuid' })
	contactId: string;

	@ManyToOne(() => Address, (address) => address.customerClaims, { cascade: true })
	@Index()
	address: Address;
	@Column({ type: 'uuid' })
	addressId: string;

	@ManyToOne(() => EcommerceOrderItem, (ecommerceOrderItem) => ecommerceOrderItem.customerClaims, {
		onDelete: 'SET NULL',
	})
	@Index()
	ecommerceOrderItem: EcommerceOrderItem;
	@Column({ type: 'uuid' })
	ecommerceOrderItemId: string;
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'customerClaimHistory', synchronize: false })
export class CustomerClaimHistory extends CustomerClaim {}
