import { PRIORITY_VALUES, type ServiceCaseStatus, type ServiceCaseType } from '@pocitarna-nx-2023/config';
import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { CustomerClaim } from './CustomerClaim';
import { EcommerceOrder } from './EcommerceOrder';
import { FileServiceCase } from './File';
import { InventoryItem } from './InventoryItem';
import { Note } from './Note';
import { ProductDefect } from './ProductDefect';
import { ServiceCaseCode } from './ServiceCaseCode';
import { ServiceCenter } from './ServiceCenter';
import { User } from './User';
import { WarrantyClaim } from './WarrantyClaim';

@Entity({ name: 'serviceCase' })
export class ServiceCase extends CommonEntity {
	@ManyToOne(() => User, (user) => user.createdBatches)
	@Index()
	createdBy: User | null;
	@Column({ type: 'uuid', nullable: true })
	createdById: string | null;

	@Column({ type: 'varchar', default: 'NEW' })
	@Index()
	status: ServiceCaseStatus;

	@Column({ type: 'varchar', default: 'BACKOFFICE' })
	@Index()
	type: ServiceCaseType;

	@Column({ type: 'int2', default: PRIORITY_VALUES.MEDIUM })
	priority: number;

	@Column({ type: 'varchar', default: '' })
	trackingCode: string;

	@OneToMany(() => ProductDefect, (productDefect) => productDefect.serviceCase)
	productDefects: ProductDefect[];

	@OneToMany(() => ProductDefect, (productDefect) => productDefect.vendorTask)
	vendorDefects: ProductDefect[];

	@OneToMany(() => EcommerceOrder, (ecommerceOrder) => ecommerceOrder.serviceCase)
	ecommerceOrders: EcommerceOrder[];

	@OneToMany(() => InventoryItem, (inventoryItem) => inventoryItem.serviceCase)
	inventoryItems: InventoryItem[];

	@ManyToOne(() => ServiceCenter, (serviceCenter) => serviceCenter.serviceCases, { onDelete: 'SET NULL' })
	@Index()
	serviceCenter: ServiceCenter | null;
	@Column({ type: 'uuid', nullable: true })
	serviceCenterId: string | null;

	@OneToMany(() => FileServiceCase, (fileServiceCase) => fileServiceCase.serviceCase)
	files: FileServiceCase[];

	@ManyToOne(() => ServiceCaseCode, (code) => code.serviceCase, { onDelete: 'CASCADE' })
	@Index()
	code: ServiceCaseCode;
	@Column({ type: 'uuid' })
	codeId: string;

	@ManyToOne(() => WarrantyClaim, (warrantyClaim) => warrantyClaim.sourceServiceCases, { cascade: true })
	@Index()
	sourceWarrantyClaim: WarrantyClaim | null;
	@Column({ type: 'uuid', nullable: true })
	sourceWarrantyClaimId: string | null;

	@ManyToOne(() => CustomerClaim, (customerClaim) => customerClaim.serviceCases, { cascade: true })
	@Index()
	sourceCustomerClaim: CustomerClaim | null;
	@Column({ type: 'uuid', nullable: true })
	sourceCustomerClaimId: string | null;

	@OneToMany(() => WarrantyClaim, (warrantyClaim) => warrantyClaim.sourceServiceCase)
	sourceWarrantyClaims: WarrantyClaim[];

	@OneToMany(() => Note, (note) => note.serviceCase)
	notes: Note[];
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'serviceCaseHistory', synchronize: false })
export class ServiceCaseHistory extends ServiceCase {}
