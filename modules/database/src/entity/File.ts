import { type BatchFileType, type CustomerClaimFileType, type ProductFileType } from '@pocitarna-nx-2023/config';
import { type MimeType } from 'file-type/core';
import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { Batch } from './Batch';
import { BatchDefect } from './BatchDefect';
import { CustomerClaim } from './CustomerClaim';
import { CustomerClaimCosmeticDefect } from './CustomerClaimCosmeticDefect';
import { Inventory } from './Inventory';
import { Product } from './Product';
import { ProductCode } from './ProductCode';
import { ProductCosmeticDefect } from './ProductCosmeticDefect';
import { ProductDefect } from './ProductDefect';
import { ServiceCase } from './ServiceCase';
import { Vendor } from './Vendor';
import { WarrantyClaim } from './WarrantyClaim';

@Entity({ name: 'file' })
export class File extends CommonEntity {
	@Column({ type: 'varchar' })
	mime: MimeType;

	@Column({ type: 'varchar' })
	@Index()
	name: string;

	@Column({ type: 'text', default: '' })
	note: string;

	@Column({ type: 'text', default: '' })
	preview: string;

	@Column({ type: 'uuid', nullable: true })
	photoSessionId?: string;

	@OneToMany(() => FileBatch, (fileBatch) => fileBatch.file)
	batches: FileBatch[];

	@OneToMany(() => FileBatchDefect, (fileBatchDefect) => fileBatchDefect.file)
	batchDefects: FileBatchDefect[];

	@OneToMany(() => FileProduct, (fileProduct) => fileProduct.file)
	products: FileProduct[];

	@OneToMany(() => FileProductDefect, (fileProductDefect) => fileProductDefect.file)
	productDefects: FileProductDefect[];

	@OneToMany(() => FileServiceCase, (fileServiceCase) => fileServiceCase.file)
	serviceCases: FileServiceCase[];

	@OneToMany(() => FileVendor, (fileVendor) => fileVendor.file)
	vendors: FileVendor[];

	@OneToMany(() => FileWarrantyClaim, (fileWarrantyClaim) => fileWarrantyClaim.file)
	warrantyClaims: FileWarrantyClaim[];

	@OneToMany(() => FileInventory, (fileInventory) => fileInventory.file)
	inventories: FileInventory[];

	@OneToMany(() => FileProductCosmeticDefect, (fileProductCosmeticDefect) => fileProductCosmeticDefect.file)
	productCosmeticDefects: FileProductCosmeticDefect[];

	@OneToMany(() => FileCustomerClaim, (fileCustomerClaim) => fileCustomerClaim.file)
	customerClaims: FileCustomerClaim[];

	@OneToMany(() => FileCustomerClaimCosmeticDefect, (fileCustomerClaimCosmeticDefect) => fileCustomerClaimCosmeticDefect.file)
	customerClaimCosmeticDefects: FileCustomerClaimCosmeticDefect[];
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'fileHistory', synchronize: false })
export class FileHistory extends File {}

@Entity({ name: 'fileBatch' })
@Index(['file', 'batch'], { unique: true })
export class FileBatch extends CommonEntity {
	@ManyToOne(() => File, (file) => file.batches, { onDelete: 'CASCADE' })
	@Index()
	file: File;
	@Column({ type: 'uuid' })
	fileId: string;

	@ManyToOne(() => Batch, (batch) => batch.files, { onDelete: 'CASCADE' })
	@Index()
	batch: Batch;
	@Column({ type: 'uuid' })
	batchId: string;

	@Column({ type: 'int', default: 0 })
	sequence: number;

	@Column({ type: 'varchar', default: 'regular' })
	type: BatchFileType;
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'fileBatchHistory', synchronize: false })
export class FileBatchHistory extends FileBatch {}

@Entity({ name: 'fileBatchDefect' })
@Index(['file', 'batchDefect'], { unique: true })
export class FileBatchDefect extends CommonEntity {
	@ManyToOne(() => File, (file) => file.batchDefects, { onDelete: 'CASCADE' })
	@Index()
	file: File;
	@Column({ type: 'uuid' })
	fileId: string;

	@ManyToOne(() => BatchDefect, (batchDefect) => batchDefect.files, { onDelete: 'CASCADE' })
	@Index()
	batchDefect: BatchDefect;
	@Column({ type: 'uuid' })
	batchDefectId: string;

	@Column({ type: 'int', default: 0 })
	sequence: number;
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'fileBatchDefectHistory', synchronize: false })
export class FileBatchDefectHistory extends FileBatchDefect {}

@Entity({ name: 'fileProduct' })
@Index(['file', 'product'], { unique: true })
export class FileProduct extends CommonEntity {
	@ManyToOne(() => File, (file) => file.products, { onDelete: 'CASCADE' })
	@Index()
	file: File;
	@Column({ type: 'uuid' })
	fileId: string;

	@ManyToOne(() => Product, (product) => product.files, { onDelete: 'CASCADE' })
	@Index()
	product: Product;
	@Column({ type: 'uuid' })
	productId: string;

	@Column({ type: 'int', default: 0 })
	sequence: number;

	@Column({ type: 'varchar', default: 'regular' })
	type: ProductFileType;
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'fileProductHistory', synchronize: false })
export class FileProductHistory extends FileProduct {}

@Entity({ name: 'fileProductCode' })
@Index(['file', 'productCode'], { unique: true })
export class FileProductCode extends CommonEntity {
	@ManyToOne(() => File, (file) => file.products, { onDelete: 'CASCADE' })
	@Index()
	file: File;
	@Column({ type: 'uuid' })
	fileId: string;

	@ManyToOne(() => ProductCode, (productCode) => productCode.files, { onDelete: 'CASCADE' })
	@Index()
	productCode: ProductCode;
	@Column({ type: 'uuid' })
	productCodeId: string;

	@Column({ type: 'int', default: 0 })
	sequence: number;
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'fileProductCodeHistory', synchronize: false })
export class FileProductCodeHistory extends FileProductCode {}

@Entity({ name: 'fileProductDefect' })
@Index(['file', 'productDefect'], { unique: true })
export class FileProductDefect extends CommonEntity {
	@ManyToOne(() => File, (file) => file.productDefects, { onDelete: 'CASCADE' })
	@Index()
	file: File;
	@Column({ type: 'uuid' })
	fileId: string;

	@ManyToOne(() => ProductDefect, (productDefect) => productDefect.files, { onDelete: 'CASCADE' })
	@Index()
	productDefect: ProductDefect;
	@Column({ type: 'uuid' })
	productDefectId: string;

	@Column({ type: 'int', default: 0 })
	sequence: number;
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'fileProductDefectHistory', synchronize: false })
export class FileProductDefectHistory extends FileProductDefect {}

@Entity({ name: 'fileServiceCase' })
@Index(['file', 'serviceCase'], { unique: true })
export class FileServiceCase extends CommonEntity {
	@ManyToOne(() => File, (file) => file.serviceCases, { onDelete: 'CASCADE' })
	@Index()
	file: File;
	@Column({ type: 'uuid' })
	fileId: string;

	@ManyToOne(() => ServiceCase, (serviceCase) => serviceCase.files, { onDelete: 'CASCADE' })
	@Index()
	serviceCase: ServiceCase;
	@Column({ type: 'uuid' })
	serviceCaseId: string;

	@Column({ type: 'int', default: 0 })
	sequence: number;
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'fileServiceCaseHistory', synchronize: false })
export class FileServiceCaseHistory extends FileServiceCase {}

@Entity({ name: 'fileVendor' })
@Index(['file', 'vendor'], { unique: true })
export class FileVendor extends CommonEntity {
	@ManyToOne(() => File, (file) => file.vendors, { onDelete: 'CASCADE' })
	@Index()
	file: File;
	@Column({ type: 'uuid' })
	fileId: string;

	@ManyToOne(() => Vendor, (vendor) => vendor.files, { onDelete: 'CASCADE' })
	@Index()
	vendor: Vendor;
	@Column({ type: 'uuid' })
	vendorId: string;

	@Column({ type: 'int', default: 0 })
	sequence: number;
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'fileVendorHistory', synchronize: false })
export class FileVendorHistory extends FileVendor {}

@Entity({ name: 'fileWarrantyClaim' })
@Index(['file', 'warrantyClaim'], { unique: true })
export class FileWarrantyClaim extends CommonEntity {
	@ManyToOne(() => File, (file) => file.warrantyClaims, { onDelete: 'CASCADE' })
	@Index()
	file: File;
	@Column({ type: 'uuid' })
	fileId: string;

	@ManyToOne(() => WarrantyClaim, (warrantyClaim) => warrantyClaim.files, { onDelete: 'CASCADE' })
	@Index()
	warrantyClaim: WarrantyClaim;
	@Column({ type: 'uuid' })
	warrantyClaimId: string;

	@Column({ type: 'int', default: 0 })
	sequence: number;
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'fileWarrantyClaimHistory', synchronize: false })
export class FileWarrantyClaimHistory extends FileWarrantyClaim {}

@Entity({ name: 'fileInventory' })
@Index(['file', 'inventory'], { unique: true })
export class FileInventory extends CommonEntity {
	@ManyToOne(() => File, (file) => file.inventories, { onDelete: 'CASCADE' })
	@Index()
	file: File;
	@Column({ type: 'uuid' })
	fileId: string;

	@ManyToOne(() => Inventory, (inventory) => inventory.files, { onDelete: 'CASCADE' })
	@Index()
	inventory: Inventory;
	@Column({ type: 'uuid' })
	inventoryId: string;

	@Column({ type: 'int', default: 0 })
	sequence: number;
}

@Entity({ name: 'fileProductCosmeticDefect' })
@Index(['file', 'productCosmeticDefect'], { unique: true })
export class FileProductCosmeticDefect extends CommonEntity {
	@ManyToOne(() => File, (file) => file.productCosmeticDefects, { onDelete: 'CASCADE' })
	@Index()
	file: File;
	@Column({ type: 'uuid' })
	fileId: string;

	@ManyToOne(() => ProductCosmeticDefect, (productCosmeticDefect) => productCosmeticDefect.files, { onDelete: 'CASCADE' })
	@Index()
	productCosmeticDefect: ProductCosmeticDefect;
	@Column({ type: 'uuid' })
	productCosmeticDefectId: string;

	@Column({ type: 'int', default: 0 })
	sequence: number;
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'fileProductCosmeticDefectHistory', synchronize: false })
export class FileProductCosmeticDefectHistory extends FileProductCosmeticDefect {}

@Entity({ name: 'fileCustomerClaim' })
@Index(['file', 'customerClaim'], { unique: true })
export class FileCustomerClaim extends CommonEntity {
	@ManyToOne(() => File, (file) => file.customerClaims, { onDelete: 'CASCADE' })
	@Index()
	file: File;
	@Column({ type: 'uuid' })
	fileId: string;

	@ManyToOne(() => CustomerClaim, (customerClaim) => customerClaim.files, { onDelete: 'CASCADE' })
	@Index()
	customerClaim: CustomerClaim;
	@Column({ type: 'uuid' })
	customerClaimId: string;

	@Column({ type: 'int', default: 0 })
	sequence: number;

	@Column({ type: 'varchar', default: 'regular' })
	type: CustomerClaimFileType;
}

@Entity({ name: 'fileCustomerClaimHistory', synchronize: false })
export class FileCustomerClaimHistory extends FileCustomerClaim {}

@Entity({ name: 'fileCustomerClaimCosmeticDefect' })
@Index(['file', 'customerClaimCosmeticDefect'], { unique: true })
export class FileCustomerClaimCosmeticDefect extends CommonEntity {
	@ManyToOne(() => File, (file) => file.customerClaimCosmeticDefects, { onDelete: 'CASCADE' })
	@Index()
	file: File;
	@Column({ type: 'uuid' })
	fileId: string;

	@ManyToOne(() => CustomerClaimCosmeticDefect, (customerClaimCosmeticDefect) => customerClaimCosmeticDefect.files, {
		onDelete: 'CASCADE',
	})
	@Index()
	customerClaimCosmeticDefect: CustomerClaimCosmeticDefect;
	@Column({ type: 'uuid' })
	customerClaimCosmeticDefectId: string;

	@Column({ type: 'int', default: 0 })
	sequence: number;
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'fileCustomerClaimCosmeticDefectHistory', synchronize: false })
export class FileCustomerClaimCosmeticDefectHistory extends FileCustomerClaimCosmeticDefect {}
