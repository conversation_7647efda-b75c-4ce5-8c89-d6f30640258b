import { type WarrantyClaimStatus } from '@pocitarna-nx-2023/config';
import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { CommonEntity } from '../utils/CommonEntity';
import { CustomerClaim } from './CustomerClaim';
import { FileWarrantyClaim } from './File';
import { InventoryItem } from './InventoryItem';
import { Note } from './Note';
import { ProductDefect } from './ProductDefect';
import { ServiceCase } from './ServiceCase';
import { User } from './User';
import { WarrantyClaimCode } from './WarrantyClaimCode';

@Entity({ name: 'warrantyClaim' })
export class WarrantyClaim extends CommonEntity {
	@ManyToOne(() => User, (user) => user.createdBatches)
	@Index()
	createdBy: User | null;
	@Column({ type: 'uuid', nullable: true })
	createdById: string | null;

	@Column({ type: 'varchar', default: 'NEW' })
	@Index()
	status: WarrantyClaimStatus;

	@Column({ type: 'varchar', default: '' })
	trackingCode: string;

	@Column({ type: 'varchar', default: '' })
	vendorRMAIdentifier: string;

	@OneToMany(() => ProductDefect, (productDefect) => productDefect.warrantyClaim)
	productDefects: ProductDefect[];

	@OneToMany(() => FileWarrantyClaim, (fileWarrantyClaim) => fileWarrantyClaim.warrantyClaim)
	files: FileWarrantyClaim[];

	@OneToMany(() => InventoryItem, (inventoryItem) => inventoryItem.warrantyClaim)
	inventoryItems: InventoryItem[];

	@ManyToOne(() => WarrantyClaimCode, (code) => code.warrantyClaim)
	@Index()
	code: WarrantyClaimCode;
	@Column({ type: 'uuid' })
	codeId: string;

	@ManyToOne(() => ServiceCase, (serviceCase) => serviceCase.sourceWarrantyClaims, { cascade: true })
	@Index()
	sourceServiceCase: ServiceCase | null;
	@Column({ type: 'uuid', nullable: true })
	sourceServiceCaseId: string | null;

	@OneToMany(() => ServiceCase, (serviceCase) => serviceCase.sourceWarrantyClaim)
	sourceServiceCases: ServiceCase[];

	@ManyToOne(() => CustomerClaim, (customerClaim) => customerClaim.warrantyClaims, { cascade: true })
	@Index()
	sourceCustomerClaim: CustomerClaim | null;
	@Column({ type: 'uuid', nullable: true })
	sourceCustomerClaimId: string | null;

	@OneToMany(() => Note, (note) => note.warrantyClaim)
	notes: Note[];
}

// Synchronization is off, because it has to be copied without constraints
@Entity({ name: 'warrantyClaimHistory', synchronize: false })
export class WarrantyClaimHistory extends WarrantyClaim {}
